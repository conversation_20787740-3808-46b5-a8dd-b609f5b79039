A Mechanical and Algorithmic Framework for the Simulation of Backpack Battles




Section 1: Foundational Game Architecture


This section defines the high-level structure of the game, establishing the state machine, core player variables, and the physical constraints of the gameplay environment. It serves as the foundational layer upon which all other mechanics are built, providing the necessary context for the detailed system analyses that follow. A precise model of this architecture is paramount for creating a simulation environment capable of accurately representing the game state for a reinforcement learning (RL) agent.


1.1 The Core Game Loop: A Finite State Machine


The gameplay of Backpack Battles unfolds as a discrete, iterative process, structured around a sequence of distinct phases. A complete game run consists of up to 18 rounds.1 This structure can be modeled as a finite state machine, where the agent's actions are primarily confined to the Shop Phase. The simulation must transition between these states based on defined trigger conditions.
* State 1: Shop Phase. This is the primary interactive phase for the player (and thus, the RL agent). During this phase, the agent uses its Gold resource to purchase items, sell existing items, and arrange them within the backpack grid.1 This phase is asynchronous and not constrained by a timer, allowing for complex decision-making without temporal pressure.3 The Shop Phase begins at the start of each new round.
* State 2: Battle Phase. Upon the agent's decision to commence combat, the environment transitions to the Battle Phase. In this state, the agent's configured backpack is automatically pitted against an asynchronously selected opponent's build.1 The battle proceeds without any agent intervention, functioning as an "autobattler" where the outcome is determined entirely by the initial state of the two opposing backpacks.1
* State 3: Combination Phase. Following the conclusion of the Battle Phase, and before the initiation of the next Shop Phase, the game enters an automatic, non-interactive Combination Phase. During this transition, any items in the agent's backpack that are placed adjacently and match a valid crafting recipe are consumed and replaced by a new, typically more powerful, item.1 Items can be explicitly locked by the agent to prevent this transformation.5
The fundamental game loop proceeds deterministically through this cycle: Shop → Battle → Combination → Shop, iterating for each round until a terminal condition for the run is met.


1.2 Player State Vector Representation


To facilitate learning, the RL agent requires a comprehensive and well-defined representation of the game state at any given time. This can be conceptualized as a state vector containing all relevant variables that describe the agent's current status and resources.
* Dynamic Resources: These variables change frequently based on game events and agent actions.
   * Health: An integer representing the player's life total. The initial value is 25. At the start of each subsequent round, the player's maximum health is set to a new, higher value according to a fixed schedule.2
   * Gold: An integer representing the primary currency for interacting with the shop. The player starts with 12 Gold, and a new, predetermined amount is awarded at the beginning of each round's Shop Phase.2 This value is modified by purchasing, selling, and specific item effects like the
Piggybank.5
   * Stamina: A floating-point value representing the resource consumed by most weapons to activate. This is composed of two sub-variables: MaxStamina and CurrentStamina. The base passive regeneration rate is 1.0 stamina per second, though this can be modified by various item effects.8
   * Mana: An integer value used as a resource for items with the Magic classification.2
   * Lives: An integer representing the number of losses permitted before the run terminates. The player starts with 5 lives (often represented as hearts), and this value is decremented upon losing a battle.3
   * Trophies: An integer representing the number of wins in the current run. The player starts with 0 trophies, and this value is incremented upon winning a battle.3
   * Static & Semi-Static Attributes: These variables define the agent's build archetype and progression through the game.
   * Class: An enumerated type representing the chosen character. The possible values are {Berserker, Pyromancer, Reaper, Ranger}.9
   * Subclass: An enumerated type representing the specialization chosen at Round 8. It defaults to None. Examples include {Beastmaster, Hunter, Vampiress, Hexblade, Blacksmith, Scalewarden}.10
   * Rank: An enumerated type representing the player's standing in the ranked matchmaking system. The hierarchy is {Bronze, Silver, Gold, Platinum, Diamond, Master, Grandmaster, Grandma}.6
   * CurrentRound: An integer from 1 to 18 indicating the current round of play.6
   * Inventory State: The most complex component of the state vector, representing the physical arrangement of all owned items.
   * BackpackGrid: A two-dimensional array or equivalent data structure representing the main inventory space where active items are placed. The state of each cell must be tracked (empty or occupied by a specific ItemID).
   * Storage: A list, array, or other collection representing the off-grid storage area. Items in storage are owned by the player but their effects are inactive until moved to the BackpackGrid.2


1.3 The Backpack Grid System: Rules of Placement and Adjacency


The central mechanic of Backpack Battles is the management of items within a grid-based inventory. The precise rules governing this system are critical for a valid simulation.
   * Grid and Expansion: The maximum potential backpack space is a grid of 63 tiles, which corresponds to a 9x7 layout.2 A player begins with a smaller subset of this space, typically 12 to 14 tiles, determined by their class-specific starting bag.2 The total available grid space is expanded by purchasing and placing
Bag items, such as the Leather Bag (adds 4 slots) or Fanny Pack (adds 2 slots).13 These bags can be freely moved and rotated on the game board, taking their contents with them, which simplifies reorganization for human players but represents a complex, multi-part action for an RL agent.4
   * Item Placement and Rotation: Every item possesses a fixed shape and size, represented as a 2D matrix of occupied grid cells (e.g., a Wooden Sword might be 1x3, a Goobert 2x2).4 Items can only be placed within the available grid tiles provided by placed bags. They can be rotated, typically in 90-degree increments, to facilitate fitting them into the backpack.10 An item's placement is defined by its top-left anchor coordinate on the grid and its current rotation state.
   * Adjacency and Synergy Connectors: The strategic depth of placement arises from synergy effects, denoted by star (★) and diamond (♦) icons in item tooltips.2 These icons represent connection points that grant bonuses when a valid item is placed in an adjacent slot.
      * Adjacency Definition: Standard adjacency is defined as two items sharing a grid edge. Diagonal adjacency is not counted unless an item's effect explicitly states so (e.g., Acorn Collar).18
      * Connection Uniqueness: An item can typically satisfy only one star and one diamond requirement on another single item. This prevents a single powerful item from activating multiple identical synergy slots on a neighbor.2
      * This system creates a directed graph within the backpack, where items are nodes and the star/diamond connections are edges. The simulation must parse this graph at the beginning of each battle to determine the full set of active synergies and initial buffs.


1.4 Game Progression and Termination Conditions


A single run of Backpack Battles is a finite sequence of rounds with clear termination conditions.
      * Resource Progression: The player's Health and Gold are not static but are replenished and increased at the start of each round according to a fixed, non-random schedule. This schedule is a fundamental constant of the game's economy and must be hardcoded into the simulation environment.
      * Termination Conditions: A run concludes when one of the following conditions is met:
      * Loss by Attrition: The run ends in a loss if the player's Lives counter reaches 0. This occurs after the player's fifth battle loss.1
      * Standard Victory: A run is considered a "win" when the player accumulates 10 Trophies by winning 10 battles.1
      * Survival Mode: Upon achieving a standard victory (10 wins), the agent is presented with a choice: end the run and claim the rank points, or enter "Survival Mode." In Survival Mode, the player continues playing through the remaining rounds until either Round 18 is complete or their remaining Lives are depleted.1 This decision carries risk, as subsequent losses can reduce the final rank reward.1
      * Draw Resolution (Fatigue): Individual battles are prevented from running indefinitely by the Fatigue mechanic. After a certain duration in a battle (referred to as "nightfall"), both combatants begin to take an increasing amount of damage over time, ensuring one player's health will eventually be depleted.2 The precise start time and scaling formula for Fatigue damage are essential parameters for the combat simulation.
________________
Table 1.1: Round-by-Round Resource Allocation
This table outlines the base Gold awarded to the player upon entering the shop each round and the player's maximum Health for the duration of that round's battle. This data is fundamental for modeling the game's economic progression and player survivability. Data sourced from.2


Round
	Gold Awarded
	Max Health
	Notes
	1
	12
	25
	Starting Round
	2
	9
	35
	

	3
	9
	45
	

	4
	9
	55
	Unique items can appear
	5
	10
	70
	

	6
	10
	85
	

	7
	11
	100
	

	8
	21
	115
	Subclass choice offered
	9
	12
	130
	

	10
	12
	150
	

	11
	13
	170
	

	12
	13
	190
	

	13
	14
	210
	

	14
	14
	230
	

	15
	15
	260
	

	16
	15
	290
	

	17
	15
	320
	

	18
	15
	350
	Final Round
	________________


Section 2: The Shop Phase: A Probabilistic Model


The Shop Phase is the crucible of strategy in Backpack Battles. The agent's success is heavily dependent on its ability to navigate this phase, making optimal decisions based on probabilistic outcomes. This section deconstructs the shop into its constituent algorithms and defines the agent's interaction model.


2.1 Shop State and Agent Action Space


The simulation must present the shop to the agent as a clearly defined state and provide a discrete set of actions that can be taken to modify that state.
      * Shop State Representation:
      * Item Slots: The shop displays a fixed number of items, typically five.2 The state of each slot includes the
ItemID, its Cost, and its SaleStatus.
      * Reroll Cost: An integer representing the current Gold cost to reroll the shop. This value is dynamic within a round.
      * Agent Resources: The agent's current Gold is the primary constraint on its actions.
         * Agent Action Space: The set of all possible actions the agent can take during the Shop Phase must be precisely defined.
         * Purchase(item_slot_index): Spends Gold equal to the item's cost and moves the item from the specified shop slot to the agent's storage.
         * Sell(item_instance_ID): Moves a specific item instance from the agent's inventory or storage to the sell area, adding 50% of the item's base Cost (rounded up) to the agent's Gold.7
         * Reroll(): Spends the current RerollCost to generate a new set of five items in the shop. The cost is 1 Gold for the first four rerolls in a given round, and 2 Gold for all subsequent rerolls within that same round.2
         * Reserve(item_slot_index, lock_status): Toggles a "Reserved" state on an item in the shop. Reserved items are not replaced during a Reroll or when a new round begins.2 This action costs no
Gold.
         * ToggleCombinationLock(item_instance_ID): Toggles a locked flag on an item within the agent's backpack. A locked item will not be consumed in the post-battle Combination Phase, even if it is part of a valid recipe.5


2.2 Item Generation Algorithm


The core of the shop is a sophisticated, multi-layered probabilistic algorithm that determines which items are presented to the agent. This algorithm is not static; it evolves as the game progresses.
            * Rarity-Based Weighted Sampling: The primary mechanism is a weighted random sampling process governed by item rarity. The probabilities for each rarity tier—Common, Rare, Epic, Legendary, and Godly—change dynamically with the CurrentRound. For instance, in Round 1, the pool is heavily skewed towards Common items (90% chance), while by Round 12, all five rarities have an equal 20% chance of appearing.2 The simulation must implement this by first selecting a rarity based on the round-specific probabilities, and then uniformly sampling an item from the pool of all available items of that chosen rarity.
            * Item Pool Filtering: The global set of all items is not always available. The pool of potential items to be sampled from is filtered by several factors before the rarity sampling occurs.
            * Class Restriction: Many items are exclusive to a specific class. For example, the Flame item is only available to the Pyromancer, and the Forging Hammer is a starting item for the Berserker.22 The simulation must maintain a list of class-specific items and filter the item pool accordingly based on the agent's chosen
Class.
            * Subclass Gating: The selection of a subclass at Round 8 is a pivotal moment that can expand the available item pool. For example, the Scalewarden subclass for the Pyromancer adds Dragon Eggs to the shop, and the Witch subclass for the Reaper adds various Plushies.11 The simulation must dynamically modify the item pool immediately after a subclass is chosen.
            * Unique Item Generation: Unique items operate on a separate, parallel system. Starting from Round 4, there is a 2% base chance for a Unique item to appear in the shop only upon entering the Shop Phase for a new round. This chance does not apply to rerolls. Furthermore, an agent can only possess one Unique item at any time; if one is owned, no others will be offered.2


2.3 Shop Sub-systems and Pity Timers


To mitigate pure randomness and ensure a degree of consistency, the shop incorporates several "pity timer" mechanics. These are deterministic counters that guarantee certain events after a specific number of trials.
               * Sales: Each item presented in the shop has a 10% base probability of being on sale for 50% of its cost, rounded up.7 Critically, there is a pity mechanism that guarantees an item will be on sale if one has not appeared within the last 16 items generated in the shop.26 The simulation must maintain a counter,
items_seen_since_last_sale, which increments for each of the five items shown in a new or rerolled shop, and resets to zero when a sale occurs.
               * Bags: Similarly, a Bag item is guaranteed to appear in the shop if one has not been seen within the last 10 items generated.26 A separate counter,
items_seen_since_last_bag, must be implemented.
               * Item Pool Blacklisting (Historical Mechanic): Analysis of community discussions reveals the past existence of a "soft blacklisting" system to prevent item repetition. The rules were described as: buying an item prevents it from appearing on the next reroll, and passing on an item prevents it from appearing for 2-3 rerolls.26 However, more recent information suggests this mechanic (
filterRejectedItems) was removed from the game.26 The removal of this system has profound implications for an RL agent's strategy. Without blacklisting, it is possible, though statistically unlikely, to see the same item in consecutive rerolls. This makes strategies that involve "fishing" for multiple copies of a single item (e.g., multiple
Whetstones for a Hero Longsword) more viable. For maximum fidelity, a simulation environment should model this as a configurable boolean parameter, config.use_shop_blacklist, which should be set to False to reflect the current state of the game.
________________
Table 2.1: Shop Item Rarity Probabilities by Round
This table provides the foundational probabilities for the shop's item generation algorithm. An RL agent must implicitly or explicitly understand these shifting odds to evaluate the long-term potential of its build and decide when it is optimal to spend gold on rerolls versus saving for later rounds when higher-rarity items are more common. Data sourced from.2


Round
	Common (%)
	Rare (%)
	Epic (%)
	Legendary (%)
	Godly (%)
	1
	90
	10
	0
	0
	0
	2
	84
	15
	1
	0
	0
	3
	75
	20
	5
	0
	0
	4
	64
	25
	10
	1
	0
	5
	45
	35
	15
	5
	0
	6
	29
	40
	20
	10
	1
	7
	20
	35
	25
	15
	5
	8
	20
	30
	25
	15
	10
	9
	20
	28
	25
	15
	12
	10
	20
	25
	25
	15
	15
	11
	20
	23
	23
	17
	17
	12-18
	20
	20
	20
	20
	20
	Table 2.2: Subclass-Gated Item Pools (Sample)
This table illustrates how subclass selection at Round 8 expands the item pool. A complete simulation must contain the full mapping for all 20 subclasses. This is a critical branching point in any run, as it fundamentally alters the set of available tools for the agent. Data sourced from.11


Class
	Subclass
	Key Item
	Unlocked Item Pool
	Ranger
	Beastmaster
	Big Bowl of Treats
	"Friends of the Forest" (e.g., Rat, Squirrel, Hedgehog)
	Ranger
	Hunter
	Piercing Arrow
	No new items, modifies existing weapon effects
	Reaper
	Witch
	Mr. Struggles
	"Plushies" (e.g., Mrs. Struggles, Miss Fortune)
	Reaper
	Venomancer
	Snake
	No new items, modifies Poison mechanics
	Berserker
	Pack Leader
	Wolf Emblem
	"Wolf Companions"
	Berserker
	Shaman
	Shaman Mask
	"Runes" (e.g., Hawk Rune, Badger Rune)
	Pyromancer
	Scalewarden
	Dragon Nest
	Dragon Eggs (Amethyst, Emerald, Sapphire)
	Pyromancer
	Cryomancer
	Frozen Flame
	Ice items (e.g., Book of Ice)
	________________


Section 3: The Combat Phase: A Continuous-Time Event Simulation


The Battle Phase in Backpack Battles is a deterministic, automated process where the outcome is entirely dictated by the initial configuration of the two competing backpacks. Unlike turn-based systems, combat unfolds in continuous time, driven by item cooldowns. A robust simulation of this phase is best achieved using a discrete event simulation model, specifically one centered around a priority queue.


3.1 Combat Initialization and "Start of Battle" Effects


The moment combat begins (time t=0), a sequence of "Start of Battle" effects resolves before any periodic actions commence. These effects can grant buffs, generate resources, or apply initial debuffs. Examples include Gloves of Haste granting a speed boost, Blood Amulet granting Vampirism, and Lucky Clover granting Luck.29
The order in which these simultaneous t=0 events resolve is of paramount importance. Evidence from community analysis suggests a subtle but critical rule: the activation order is determined by the order in which the items were placed into the backpack.32 An item placed in an earlier round or earlier within the same Shop Phase will have its "Start of Battle" effect resolve before an item placed later. This implies a "First-In, First-Out" (FIFO) or "Insertion Order" priority system.
A correct simulation must therefore not only track the items in the backpack but also maintain a timestamp or a simple incrementing counter for each placement action. At t=0, the simulation would build a list of all items with "Start of Battle" effects, sort this list by their placement timestamp, and execute their effects sequentially in that order.


3.2 The Action & Event Queue: A Priority Queue Model


To manage the asynchronous and continuous nature of item activations, a priority queue is the ideal data structure for the core combat loop. Each element in the queue represents a future event.
                  * Event Tuple Structure: Each event in the queue can be represented by a tuple: (activation_time, priority, item_instance_ID, effect_function).
                  * activation_time: A float representing the simulation time at which this event should occur. This is the primary key for sorting the queue.
                  * priority: An integer used as a tie-breaker if two events have the exact same activation_time. This could be the item's placement timestamp to maintain the insertion-order rule.
                  * item_instance_ID: A unique identifier for the item triggering the event.
                  * effect_function: A reference to the function that executes the item's effect (e.g., deal_damage, apply_heal, generate_mana).
                  * Simulation Loop:
                  1. Initialization: At t=0, after resolving "Start of Battle" effects, the priority queue is populated. For every item with a periodic effect (e.g., a weapon with a cooldown, a food item that heals), an initial event is created and inserted into the queue. The initial activation_time is the item's EffectiveCooldown. A small random variance (+/- 10%) should be added to this initial cooldown to break perfect symmetry and prevent infinite loops in mirror matches, as suggested by community findings.34
                  2. Execution: The main loop continuously extracts the event with the lowest activation_time from the queue.
                  3. Clock Advancement: The global simulation clock is advanced to the activation_time of the extracted event.
                  4. Effect Resolution: The corresponding effect_function for the item_instance_ID is executed. This may modify the state of either combatant (e.g., reduce health, add buffs).
                  5. Re-queuing: If the item's effect is periodic, a new event for that item is calculated and inserted back into the queue. Its new activation_time will be current_time + item.EffectiveCooldown.
                  6. Termination Check: After each event, the simulation checks for combat termination conditions (i.e., if either player's health is at or below zero).


3.3 Resource Dynamics: Stamina and Mana


Combat resources are consumed and generated dynamically, directly impacting item activations.


3.3.1 Stamina Model


Stamina is the primary constraint for weapon usage. A precise model is essential for accurately calculating damage output over time.
                  * MaxStamina: A base value that can be increased by specific items, such as the Stamina Sack.35
                  * PassiveStaminaRegen: The base regeneration rate is 1.0 stamina per second.8 This rate can be modified by percentage-based effects from items like Topaz gemstones.36 The formula is
EffectiveRegen = BaseRegen * (1 + PercentageBonuses).
                  * StaminaCost: Each weapon has a static stamina cost per activation.
                  * StaminaCostPerSecond: A derived statistic shown to the player for convenience, calculated as StaminaCostPerSecond=EffectiveCooldownStaminaCost​.8
                  * Out of Stamina State: This is a critical mechanic. If a weapon's cooldown elapses but the player's CurrentStamina is less than the weapon's StaminaCost, the activation is paused. The weapon will not attack, and its cooldown timer will not restart. It will remain in a "ready" state, continuously checking if enough stamina is available. Once CurrentStamina is sufficient, the attack will execute immediately, and only then will its cooldown timer reset and begin counting down for the next attack.37 This delay represents a direct and significant loss of potential damage per second (DPS).


3.3.2 Mana Model


Mana is a simpler, discrete resource used by magic items.
                     * CurrentMana: An integer resource.
                     * Generation: Generated by items like Mana Orb (probabilistic gain on activation) or Blueberries (deterministic gain on activation).2
                     * Consumption: Consumed by items to trigger powerful secondary effects, such as a Lightsaber inflicting Blind or a Glowing Crown granting Invulnerability.25


3.4 Damage Calculation and Resolution: A Multi-Stage Pipeline


Every damaging attack proceeds through a fixed, sequential pipeline of calculations and checks.
                     1. Base Damage Calculation: The initial damage value is determined. RawDamage = RandomInteger(Weapon.MinDamage, Weapon.MaxDamage) + Player.EmpowerStacks.
                     2. Critical Hit Check: A probabilistic check for a critical hit is performed. The CritChance is influenced by Luck, class bags, and other item effects. If the roll succeeds, the damage is multiplied by a CritMultiplier (typically 2.0).2
ModifiedDamage = IsCrit? RawDamage * 2.0 : RawDamage.
                     3. Accuracy Check: A probabilistic check for hitting the target is performed. The base Accuracy of the weapon is modified by the attacker's Luck and the defender's Blind status. The formula is FinalAccuracy = BaseAccuracy * (1 + 0.05 * Attacker.Luck) * (1 - 0.05 * Defender.Blind). If this roll fails, the attack misses, and the damage pipeline terminates with zero damage dealt.
                     4. Block Application: The defender's Block value is subtracted from the incoming damage. FinalDamage = max(0, ModifiedDamage - Defender.Block). The Block consumed is equal to the ModifiedDamage before subtraction. Some rare effects may bypass Block entirely.2
                     5. Health Reduction: The defender's Health is reduced by the FinalDamage.
                     6. "On Hit" Effect Triggering: After damage is applied, any "On Hit" effects of the attacking weapon are resolved. This includes applying debuffs like Poison, gaining buffs like Vampirism, or triggering secondary effects on other items.41


3.5 Compendium of Status Effects


The vast array of buffs and debuffs is central to the game's complexity. A simulation requires a complete and precise mathematical definition for every status effect.
________________
Table 3.1: Status Effect Mechanics Compendium (Sample)
This table provides a structured definition for key status effects. A complete simulation requires an exhaustive version of this table covering all buffs and debuffs mentioned in the game's documentation. Data sourced from.2


Effect Name
	Type
	Stacking
	Effect Formula / Description
	Empower
	Buff
	Additive
	Increases weapon damage by 1 per stack. Damage += Empower_Stacks.
	Heat
	Buff
	Additive
	All item cooldowns trigger faster. EffectiveCooldown = BaseCooldown / (1 + 0.02 * Heat_Stacks).
	Luck
	Buff
	Additive
	Increases accuracy by 5% per stack. AccuracyModifier *= (1 + 0.05 * Luck_Stacks).
	Regeneration
	Buff
	Additive
	Heals 1 health per stack every 2 seconds.
	Spikes
	Buff
	Additive
	When hit by a melee attack, reflect damage equal to min(IncomingDamage, Spikes_Stacks).
	Vampirism
	Buff
	Additive
	When hitting with a melee weapon, heal for an amount equal to min(DamageDealt, Vampirism_Stacks).
	Blind
	Debuff
	Additive
	Decreases accuracy by 5% per stack. AccuracyModifier *= (1 - 0.05 * Blind_Stacks).
	Cold
	Debuff
	Additive
	All item cooldowns trigger slower. EffectiveCooldown = BaseCooldown * (1 + 0.02 * Cold_Stacks).
	Poison
	Debuff
	Additive
	Deals 1 damage per stack every 2 seconds. This damage bypasses Block.
	Stun
	Debuff
	Duration
	Pauses all item cooldowns for the duration of the stun.
	Invulnerability
	Buff
	Duration
	Prevents all incoming damage for the duration.
	________________


3.6 Combat Termination


A battle concludes when a definitive winner is determined.
                        * Primary Condition: The battle ends immediately when one player's Health is reduced to 0 or less.
                        * Special Condition (Reincarnate): Certain items, like the Phoenix or the Pyromancer's Dark Lantern subclass item, grant the Reincarnate ability. When a player with this ability would be defeated, their death is prevented, they are healed for a certain amount, and the battle continues.2 The simulation must check for this status before declaring a winner.
                        * Tie-Breaker (Fatigue): To prevent stalemates, the Fatigue mechanic begins after a predetermined duration ("nightfall"). It deals a ramping amount of unblockable damage to both players, ensuring an eventual conclusion.2 The simulation must track battle time and apply Fatigue damage according to its specific scaling formula.


Section 4: The Item Compendium


This section outlines the structure for a comprehensive database of all game items. An exhaustive and accurate item database is the bedrock of a high-fidelity simulation, as the game's logic is almost entirely driven by the properties and interactions of these objects.


4.1 Item Data Schema


A standardized data schema is necessary to represent every item consistently. This schema should be robust enough to capture all mechanical properties. The following structure is proposed:
                        * ItemID: A unique integer or string identifier.
                        * ItemName: The in-game name of the item (e.g., "Hero Sword").
                        * Rarity: An enum {Common, Rare, Epic, Legendary, Godly, Unique}.
                        * Class: An enum {Neutral, Berserker, Pyromancer, Reaper, Ranger}.
                        * Type: An enum categorizing the item's primary function {Weapon, Armor, Helmet, Shoes, Gloves, Shield, Bag, Pet, Food, Potion, Gemstone, Accessory, Playing Card}.
                        * Subtype: A list of tags for more specific mechanics {Melee, Ranged, Fire, Ice, Holy, Dark, Nature, Vampiric, Magic, Effect}.
                        * Cost: The base Gold cost in the shop.
                        * GridShape: A 2D boolean array defining the item's physical shape and size (e.g., [[true], [true], [true]] for a 1x3 item).
                        * Sockets: An integer representing the number of gem sockets on the item.
                        * InShop: A boolean indicating if the item can appear naturally in the shop.
                        * Stats: A dictionary of numerical properties. Example: {"DamageMin": 8, "DamageMax": 12, "Cooldown": 1.5, "StaminaCost": 1.0, "Accuracy": 95}.
                        * Effects: A list of dictionaries, where each dictionary defines a specific mechanical effect. Example: ``.


4.2 Neutral Items Database


This constitutes the largest portion of the item compendium, containing all items available to any class, provided their rarity and other conditions are met. This includes foundational items like the Wooden Sword, Pan, Broom, Gloves of Haste, Leather Boots, Health Potion, and their numerous upgrades and combinations.14


4.3 Class-Specific Item Databases


Four distinct databases are required, one for each class, cataloging the items that are exclusive to them (unless a Rainbow Badge is owned). These items are central to defining the unique playstyles of each class.22
                        * Berserker: Axe, Cheese, Spiked Collar, Puppies, Runes.23
                        * Pyromancer: Flame, Chili Pepper, Draconic Orb, Dragon Eggs.22
                        * Reaper: Fly Agaric, Deck of Cards, Death Scythe, Demonic Flask.49
                        * Ranger: Lucky Clover, Carrot, Shortbow, Bow and Arrow, Hedgehog.48


4.4 Gemstone Mechanics


Gemstones are a unique item type that requires special handling in the simulation due to their tiered nature and context-dependent effects.
                        * Rarity Progression: Gems exist in five tiers of quality: Chipped, Flawed, Regular, Flawless, and Perfect. Combining two identical gems of the same type and tier produces a single gem of the next higher tier. For example, 2x Flawed Ruby -> 1x Regular Ruby.50
                        * Context-Dependent Effects: The mechanical effect of a gemstone is determined by where it is socketed. For example, a Ruby socketed into a weapon grants lifesteal on hit, but when socketed into armor, it increases all healing received.28 A gem simply placed in the backpack (not in a socket) has a third, different effect.7 The simulation must implement a function
get_gem_effect(gem_type, gem_rarity, socket_context) to correctly apply these varying bonuses.
________________
Table 4.1: Item Database Sample - Weapons (Neutral)
This table provides a sample of the data structure for the item compendium, focusing on a few key neutral weapons. A complete database would contain hundreds of entries across all item types.
ItemName
	Rarity
	Cost
	Damage
	Stamina
	Accuracy
	Cooldown
	Sockets
	Effects
	Wooden Sword
	Common
	3
	2-4
	1.0
	90%
	1.8s
	1
	None
	Hero Sword
	Epic
	7
	2-4
	0.7
	90%
	1.4s
	1
	Start of Battle: All friendly weapons gain +1 damage. 43
	Thorn Whip
	Epic
	8
	4-9
	2.2
	80%
	2.2s
	2
	On Hit: Gain 1 Spikes. Deals +1 damage per Spikes you have. 41
	Crossblades
	Godly
	38
	16-19
	1.0
	100%
	1.4s
	3
	Start of Battle: Starred weapon gains +10 damage; Diamond item triggers 60% faster. On Hit: Gain +1 damage and trigger 4% faster (permanent). 51
	Lightsaber
	Godly
	12
	8-12
	1.0
	95%
	1.5s
	2
	Use 3 Regeneration: Inflict 8 Blind for 6s. Deals +1 damage per Blind on opponent. 40
	________________


Section 5: Character Archetypes and Specializations


The initial choice of a character class, and the subsequent choice of a subclass, are the most significant decisions an agent makes, defining the core mechanics and available item pools for the entire run.


5.1 Base Class Definitions


Each of the four playable classes begins with a choice between two unique starting bags and item loadouts. These bags are not merely containers; they provide powerful, build-defining passive effects.
                           * Ranger: Starts with a choice between the Ranger Bag or Vineweave Basket.48
                           * Ranger Bag: Grants a 10% critical hit chance to all weapons placed at least partially inside it. This bonus is further increased by 3% for each stack of Luck the Ranger possesses.18 This establishes the Ranger's identity as a critical-hit-focused class.
                           * Reaper: Starts with a choice between the Storage Coffin or Relic Case.49
                           * Storage Coffin: Grants a 25% chance to inflict 1 Poison whenever any item inside it activates.49 This encourages builds with many fast-activating items to stack the damage-over-time debuff.
                           * Berserker: Starts with a choice between the Duffle Bag or Utility Pouch.23
                           * Duffle Bag: When the Berserker's health drops below 50%, they enter Battle Rage for 5 seconds. During Battle Rage, all items inside the bag trigger 30% faster, and the Berserker takes 20% reduced damage.10 This defines the Berserker's high-risk, high-reward playstyle.
                           * Pyromancer: Starts with a choice between the Fire Pit or Offering Bowl.22
                           * Fire Pit: Automatically generates a Flame item for 1 Gold upon entering the shop. It also grants +4 maximum health for each Fire item placed inside it.13 This synergizes with the Pyromancer's
Heat-stacking identity.


5.2 Subclass System Analysis


At the start of Round 8, the shop is replaced with a special selection of five unique, class-specific items. Purchasing one of these items locks in the player's subclass for the remainder of the run.11 This choice is a major strategic inflection point. Each subclass item provides a powerful, build-defining effect, and some unlock new items in the shop.
A complete simulation must have a database of all 20 subclass items and their effects. Examples include:
                              * Berserker Subclasses:
                              * Brass Knuckles (Fighter): Grants stun chance and scales with Battle Rage.28
                              * Anvil (Blacksmith): Massively buffs a single weapon based on adjacent crafted items.28
                              * Deerwood Guardian (Chieftain): Extends Battle Rage duration with Nature items and provides healing.28
                              * Wolf Emblem (Pack Leader): Unlocks Wolf companions and grants critical chance based on pets.28
                              * Shaman Mask (Shaman): Unlocks Runes and converts Luck into random buffs.28
                              * Pyromancer Subclasses:
                              * Dark Lantern (Ashbringer): A high-risk subclass that starts the battle at 50% health but grants a Reincarnate ability.24
                              * Dragon Nest (Scalewarden): Unlocks Dragon Eggs in the shop and provides buffs for a pet-based strategy.24
                              * Friendly Fire (Firebender): Consumes Mana to generate large amounts of Heat.24
                              * Burning Banner (Crusader): A defensive subclass focused on Holy items and manipulating buffs/debuffs.24
                              * Frozen Flame (Cryomancer): Unlocks Ice items and converts Heat into the opponent-slowing Cold debuff.24
                              * Reaper Subclasses:
                              * Cauldron (Alchemist): Upgrades adjacent potions and provides random resource generation.25
                              * Nocturnal Lock Lifter (Vampiress): Greatly enhances Vampirism and lifesteal builds.25
                              * Mr. Struggles (Witch): Focuses on Fatigue damage and unlocks Plushie items.25
                              * Cursed Dagger (Hexblade): A powerful, stamina-free weapon that excels at applying debuffs.25
                              * Snake (Venomancer): Prevents opponent's Poison cleanse based on Luck and enhances pet synergies.25
                              * Ranger Subclasses:
                              * Big Bowl of Treats (Beastmaster): Unlocks "Friends of the Forest" pets and enhances Food and Pet items.18
                              * Yggdrasil Leaf (Lifebinder): A powerful healing and support subclass that uses Mana and Nature items.31
                              * Piercing Arrow (Hunter): Aims to maximize the damage of a single powerful weapon with high critical damage.18
                              * Poison Ivy (Pathfinder): Focuses on a damage-over-time strategy using Spikes and Poison.18
                              * Mega Clover (Grovekeeper): Generates Lucky Clovers and provides a massive buff shower upon reaching high Luck stacks.18


Section 6: The Combination System


Item combination, or crafting, is a primary vector for power progression, allowing the agent to transform common and rare items into powerful epic, legendary, and godly equipment that may not be available in the shop. The system is governed by a simple, deterministic set of rules.


6.1 Recipe Activation Logic


The combination process is not an action the agent takes but an automatic event that occurs in the transition phase between a battle ending and the next shop beginning.
                              * Activation Condition: A recipe is activated if all of its required ingredient items are present in the agent's backpack and are placed in grid cells that are mutually adjacent.5 Adjacency requires that the items' occupied grid shapes share at least one edge.
                              * Catalysts: Certain recipes require a catalyst item. The catalyst must also be adjacent to the other ingredients, but it is not consumed during the combination process. A prime example is the Berserker's Forging Hammer, which is used to upgrade numerous pieces of gear.5
                              * Combination Lock: An agent can explicitly prevent a combination by toggling a locked flag on any of the ingredient items. If any required ingredient is locked, the combination will not occur.5 This is a crucial action for an agent that may wish to use a lower-tier item for its specific effect temporarily before upgrading it later.
                              * Resolution: Upon activation, all non-catalyst ingredient items are removed from the backpack, and the new, crafted item is placed in the inventory. If there is sufficient contiguous space where the ingredients were, the new item appears there; otherwise, it is moved to storage.57
________________
Table 6.1: Master Recipe List (Sample)
This table provides a sample of the game's crafting recipes. A complete simulation requires an exhaustive list of all possible combinations. The recipes define the "tech tree" that an RL agent must learn to navigate to achieve powerful late-game builds. Data sourced from.5


Resulting Item
	Type
	Ingredient 1
	Ingredient 2
	Ingredient 3
	Catalyst
	Class
	Hero Sword
	Weapon
	Wooden Sword
	Whetstone
	Whetstone
	-
	Neutral
	Hero Longsword
	Weapon
	Hero Sword
	Whetstone
	Whetstone
	-
	Neutral
	Falcon Blade
	Weapon
	Hero Sword
	Gloves of Haste
	Gloves of Haste
	-
	Neutral
	Crossblades
	Weapon
	Falcon Blade
	Hero Longsword
	-
	-
	Neutral
	Torch
	Weapon
	Wooden Sword
	Lump of Coal
	-
	-
	Neutral
	Burning Torch
	Weapon
	Torch
	(Any Fire Item)
	-
	-
	Neutral
	Magic Torch
	Weapon
	Torch
	Mana Potion
	-
	-
	Neutral
	Poison Dagger
	Weapon
	Dagger
	Pestilence Flask
	-
	-
	Neutral
	Bloody Dagger
	Weapon
	Dagger
	Blood Amulet
	-
	-
	Neutral
	Bloodthorne
	Weapon
	Hungry Blade
	Thorn Whip
	-
	-
	Neutral
	Manathirst
	Weapon
	Hungry Blade
	Mana Orb
	-
	-
	Neutral
	Spiked Shield
	Shield
	Wooden Buckler
	Walrus Tusk
	-
	-
	Neutral
	Steel Goobert
	Pet
	Goobert
	Hero Sword
	-
	-
	Neutral
	Strong Health Potion
	Potion
	Health Potion
	Healing Herbs
	-
	-
	Neutral
	Mana Potion
	Potion
	Health Potion
	Blueberries
	-
	-
	Neutral
	Vampiric Armor
	Armor
	Leather Armor
	Blood Amulet
	-
	-
	Neutral
	Sun Armor
	Armor
	Holy Armor
	Flame
	Flame
	-
	Pyromancer
	Molten Spear
	Weapon
	Spear
	Flame
	Flame
	-
	Pyromancer
	Double Axe
	Weapon
	Axe
	Axe
	-
	-
	Berserker
	Dragonscale Armor
	Armor
	Leather Armor
	-
	-
	Forging Hammer
	Berserker
	Fortuna's Grace
	Weapon
	Bow and Arrow
	Lucky Clover
	Lucky Clover
	-
	Ranger
	Poison Goobert
	Pet
	Goobert
	Fly Agaric
	Fly Agaric
	-
	Reaper
	________________


Section 7: Opponent Simulation Model


A reinforcement learning agent learns through interaction with its environment. In Backpack Battles, the "environment" includes not just the game's rules but also the opponents it faces. Therefore, creating a realistic model of the opponent is as crucial as accurately simulating the agent's own mechanics. A simplistic or naive opponent model will lead to the agent learning strategies that are not robust against real-world competition.


7.1 Asynchronous Matchmaking


The game's PvP system is asynchronous. The agent does not compete against another player in real-time. Instead, it is matched against a "ghost" or a saved snapshot of another player's backpack build from a similar rank and round.1 This design choice has significant implications for simulation. It means the opponent's build is static throughout the battle and does not react to the agent's actions. The simulation environment must therefore include a module for generating or loading these opponent backpack states.


7.2 Opponent Build Progression and Selection Bias


A critical factor often overlooked in simple simulations is selection bias. The pool of potential opponents is not a uniform random sample of all possible builds. An opponent faced in Round 12 is, by definition, a build that was strong enough to survive the preceding 11 rounds.59 This creates a natural and powerful filtering effect. Early-round opponents may have disjointed or experimental builds, while late-round opponents are far more likely to feature coherent, optimized, and meta-aligned strategies.59
Failing to model this progression will result in an environment with a flawed difficulty curve. An agent trained against randomly generated opponents at all rounds would not be prepared for the sharp increase in strategic coherence and power found in the later stages of a real game. To create a realistic training environment, the opponent generation model must evolve with the round number. Several approaches could be implemented:
                              * Heuristic-Based Generation: A set of scripted bots could be designed, each programmed to follow a common strategic archetype (e.g., "force poison daggers," "stack food and pan," "build dragons"). These bots would play through runs, and their surviving backpack states at each round would be saved to create a pool of opponents for the RL agent to draw from.
                              * Self-Play: A more advanced approach involves using previous iterations of the RL agent itself to generate the opponent pool. As the agent becomes stronger, the pool of opponents it faces also becomes stronger, creating a co-evolutionary arms race that can lead to the discovery of very robust strategies. This is a common technique in training agents for complex games like Go or chess.
                              * Replay-Based Seeding: The most direct way to ensure realism is to use data from actual human players. A dataset of successful builds could be scraped from community platforms (such as bpb-builds.vercel.app, mentioned in 60) and categorized by class, subclass, and round number. The simulation could then sample directly from this library of real-world builds to generate its opponents.


7.3 Rank-Based Difficulty Scaling


In addition to round progression, opponent difficulty is also modulated by the player's matchmaking rank.12 Players in higher ranks, such as Diamond and Master, face opponents with significantly more optimized and powerful builds.12
Furthermore, there is evidence that the game implements a handicapping system for players at lower ranks. This system may actively weaken the selected opponent's build, for example, by removing non-weapon items, to ensure that newer players can achieve the 10 wins required to progress.19
The opponent simulation model must account for this. The generation process should be parameterized by the agent's current Rank. For lower ranks, the generator could select from a pool of weaker builds or apply a "handicap" function that randomly removes or downgrades items from a standard opponent build. For higher ranks, it should exclusively use the most powerful and optimized builds available in its library. This ensures the agent is trained against a difficulty level appropriate to its current learned skill, providing a smooth and realistic learning curriculum.


Conclusion


This framework provides a pedantically detailed specification for the construction of a high-fidelity simulation environment for Backpack Battles. The analysis has deconstructed the game into its core systems: the foundational architecture of the game loop and state representation; the probabilistic algorithms of the Shop Phase; the continuous-time, event-driven mechanics of the Battle Phase; the exhaustive databases of items, recipes, and character classes; and a model for realistic opponent generation.
The key complexities identified for simulation are the management of continuous time in combat, the precise ordering of simultaneous "Start of Battle" effects, and the accurate modeling of probabilistic systems with underlying pity timers in the shop. The proposed solutions—an insertion-order priority queue for combat events and a multi-layered, filtered sampling algorithm for the shop—provide a robust blueprint for implementation.
By adhering to the formulas, state definitions, and algorithmic logic presented herein, it is possible to create a simulation environment that accurately mirrors the deterministic and stochastic rules of Backpack Battles. Such an environment is an essential prerequisite for the successful application of reinforcement learning techniques, enabling an agent to explore the vast strategic space of the game and discover optimal policies for item selection, placement, and economic management. The exclusion of player strategy in favor of raw mechanics ensures that the resulting simulation is a pure representation of the game's ruleset, providing a clean and unbiased foundation for AI research and development.
Works cited
                              1. en.wikipedia.org, accessed June 26, 2025, https://en.wikipedia.org/wiki/Backpack_Battles
                              2. The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/
                              3. No idea how to play and no instructions :: Backpack Battles General Discussions, accessed June 26, 2025, https://steamcommunity.com/app/2427700/discussions/0/4290313152625938174/
                              4. Backpack Battles Early Access Review - IGN, accessed June 26, 2025, https://www.ign.com/articles/backpack-battles-early-access-review
                              5. Recipe - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Recipe
                              6. Game Mechanics - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Game_Mechanics
                              7. Items - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Items
                              8. Semi-Beginner Here: Can Someone Explain How to Figure Out Weapon Stamina? - Reddit, accessed June 26, 2025, https://www.reddit.com/r/BackpackBrawl/comments/1i5ow0k/semibeginner_here_can_someone_explain_how_to/
                              9. Characters - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Characters
                              10. Backpack Battles: 9 Beginner Tips - TheGamer, accessed June 26, 2025, https://www.thegamer.com/backpack-battles-beginner-tips-tricks/
                              11. Subclass - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Subclass
                              12. Backpack Battles: Ranks, Explained - TheGamer, accessed June 26, 2025, https://www.thegamer.com/backpack-battles-ranks-explained-guide/
                              13. Bag - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Bag
                              14. Fanny Pack - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Fanny_Pack
                              15. Button that puts all items into storage instantly :: Backpack Battles General Discussions, accessed June 26, 2025, https://steamcommunity.com/app/2427700/discussions/0/3883851663709484398/
                              16. Goobert - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Goobert
                              17. Guide :: Game Basics, Clarifications, Recipes - Steam Community, accessed June 26, 2025, https://steamcommunity.com/sharedfiles/filedetails/?id=3187896204
                              18. Backpack Battles: Ranger Build Guide - TheGamer, accessed June 26, 2025, https://www.thegamer.com/backpack-battles-ranger-build-guide/
                              19. The ranked system needs a change : r/BackpackBattles - Reddit, accessed June 26, 2025, https://www.reddit.com/r/BackpackBattles/comments/1eu6i0a/the_ranked_system_needs_a_change/
                              20. Do you ever "take the win"? : r/BackpackBattles - Reddit, accessed June 26, 2025, https://www.reddit.com/r/BackpackBattles/comments/180esfb/do_you_ever_take_the_win/
                              21. Manage your inventory to PvP glory in the demo of autobattler Backpack Battles | PC Gamer, accessed June 26, 2025, https://www.pcgamer.com/manage-your-inventory-to-pvp-glory-in-the-demo-of-autobattler-backpack-battles/
                              22. Pyromancer - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Pyromancer
                              23. Berserker - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Berserker
                              24. Backpack Battles: Class Guide - TheGamer, accessed June 26, 2025, https://www.thegamer.com/backpack-battles-class-guide/
                              25. Backpack Battles Reaper Build Guide - IGN, accessed June 26, 2025, https://www.ign.com/wikis/backpack-battles/Backpack_Battles_Reaper_Build_Guide
                              26. Advanced Shop Mechanics : r/BackpackBattles - Reddit, accessed June 26, 2025, https://www.reddit.com/r/BackpackBattles/comments/186evd9/advanced_shop_mechanics/
                              27. Item pools :: Backpack Battles General Discussions - Steam Community, accessed June 26, 2025, https://steamcommunity.com/app/2427700/discussions/0/7204143288005128450/
                              28. Backpack Battles Berserker Build Guide - IGN, accessed June 26, 2025, https://www.ign.com/wikis/backpack-battles/Backpack_Battles_Berserker_Build_Guide
                              29. Gloves of Haste - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Gloves_of_Haste
                              30. Blood Amulet - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Blood_Amulet
                              31. Backpack Battles Ranger Build Guide - IGN, accessed June 26, 2025, https://www.ign.com/wikis/backpack-battles/Backpack_Battles_Ranger_Build_Guide
                              32. Present - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Present
                              33. Unclear Item Activation Order :: Backpack Battles Bugs & Issues - Steam Community, accessed June 26, 2025, https://steamcommunity.com/app/2427700/discussions/1/4290314644728213310/
                              34. First round, exact same setup. I do understand that crit/miss is a possibility on attacks and that the items have damage ranges e.g. 2-4 damage. In this case though I win because I have 10 attacks and the opponent only 9. Why do I attack faster than the opponent? : r/BackpackBattles - Reddit, accessed June 26, 2025, https://www.reddit.com/r/BackpackBattles/comments/17ppm8v/first_round_exact_same_setup_i_do_understand_that/
                              35. Stamina Sack - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Stamina_Sack
                              36. Two other questions :: Backpack Battles General Discussions - Steam Community, accessed June 26, 2025, https://steamcommunity.com/app/2427700/discussions/0/595147245062505890/
                              37. The Ultimate Stamina guide for Backpack Brawl - YouTube, accessed June 26, 2025, https://www.youtube.com/watch?v=5c1z9T8mpYE
                              38. Stamina regeneration ? : r/BackpackBattles - Reddit, accessed June 26, 2025, https://www.reddit.com/r/BackpackBattles/comments/1bbzp24/stamina_regeneration/
                              39. Mana Orb - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Mana_Orb
                              40. Lightsaber - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Lightsaber
                              41. Thorn Whip - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Thorn_Whip
                              42. Knowledge about game mechanics : r/BackpackBrawl - Reddit, accessed June 26, 2025, https://www.reddit.com/r/BackpackBrawl/comments/1da3w3c/knowledge_about_game_mechanics/
                              43. Hero Sword - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Hero_Sword
                              44. Health Potion - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Health_Potion
                              45. Leather Boots - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Leather_Boots
                              46. Pan - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Pan
                              47. Broom - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Broom
                              48. Ranger - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Ranger
                              49. Reaper - The Backpack Battles Wiki - wiki.gg, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Reaper
                              50. All Backpack Battles recipes list - PCGamesN, accessed June 26, 2025, https://www.pcgamesn.com/backpack-battles/recipes
                              51. Crossblades - The Backpack Battles Wiki, accessed June 26, 2025, https://backpackbattles.wiki.gg/wiki/Crossblades
                              52. Backpack Battles: Reaper Build Guide - TheGamer, accessed June 26, 2025, https://www.thegamer.com/backpack-battles-best-reaper-subclass-build/
                              53. Backpack Battles: Pyromancer Build Guide - TheGamer, accessed June 26, 2025, https://www.thegamer.com/backpack-battles-pyromancer-build-guide/
                              54. Backpack Battle Subclass Guide - iGV Blog, accessed June 26, 2025, https://blog.igv.com/backpack-battle-subclass-guide/
                              55. Backpack Battles Pyromancer Build Guide - IGN, accessed June 26, 2025, https://www.ign.com/wikis/backpack-battles/Backpack_Battles_Pyromancer_Build_Guide
                              56. Backpack Battles Recipe List - IGN, accessed June 26, 2025, https://www.ign.com/wikis/backpack-battles/Backpack_Battles_Recipe_List
                              57. Patch Review 0.5.4 : r/BackpackBattles - Reddit, accessed June 26, 2025, https://www.reddit.com/r/BackpackBattles/comments/17efuxv/patch_review_054/
                              58. What is the progression system? :: Backpack Battles General Discussions - Steam Community, accessed June 26, 2025, https://steamcommunity.com/app/2427700/discussions/0/4290313152636615559/
                              59. Share your "thats not legit" opponents :: Backpack Battles General Discussions, accessed June 26, 2025, https://steamcommunity.com/app/2427700/discussions/0/3955910653938468691/
                              60. Best Backpack Battles builds and meta strategies, accessed June 26, 2025, https://bpb-builds.vercel.app/builds
                              61. Backpack Battles 101! Beginner & Intermediate Guide - YouTube, accessed June 26, 2025, https://www.youtube.com/watch?v=Wb6-dEN4p-U