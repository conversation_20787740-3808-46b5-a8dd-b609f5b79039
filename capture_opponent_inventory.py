import os
import time
import json
import uuid
import keyboard
import mss
from PIL import Image
from ultralytics import Y<PERSON><PERSON>

# --- CONFIGURATION ---
HOTKEY = "f11"
COORDINATES_FILE = "opponent_grid_coordinates.json"
MODEL_PATH = "runs/detect/train/weights/best.pt" # Path to your trained YOLO model
CLASSES_FILE = "data/object_detection/classes.txt"
OUTPUT_DIR = "opponent_layouts"
GRID_DIMS = (9, 7) # Assuming opponent has the same grid size
# ---------------------

def load_json(file_path):
    """Loads data from a JSON file."""
    with open(file_path, 'r') as f:
        return json.load(f)

def get_grid_cell_bounds(coords, row, col, total_rows, total_cols):
    """Calculates the pixel boundaries of a specific grid cell."""
    tl_x, tl_y = coords['top_left']
    tr_x, tr_y = coords['top_right']
    bl_x, bl_y = coords['bottom_left']
    br_x, br_y = coords['bottom_right']

    def interpolate(p1, p2, factor):
        return p1 + (p2 - p1) * factor

    # Top and bottom row coordinates for the given column
    x_top = interpolate(tl_x, tr_x, col / total_cols)
    y_top = interpolate(tl_y, tr_y, col / total_cols)
    x_bottom = interpolate(bl_x, br_x, col / total_cols)
    y_bottom = interpolate(bl_y, br_y, col / total_cols)

    # Final coordinates for the top-left corner of the cell
    cell_tl_x = interpolate(x_top, x_bottom, row / total_rows)
    cell_tl_y = interpolate(y_top, y_bottom, row / total_rows)

    # Top and bottom row coordinates for the next column
    x_top_next = interpolate(tl_x, tr_x, (col + 1) / total_cols)
    y_top_next = interpolate(tl_y, tr_y, (col + 1) / total_cols)
    x_bottom_next = interpolate(bl_x, br_x, (col + 1) / total_cols)
    y_bottom_next = interpolate(bl_y, br_y, (col + 1) / total_cols)

    # Final coordinates for the bottom-right corner of the cell
    cell_br_x = interpolate(x_top_next, x_bottom_next, (row + 1) / total_rows)
    cell_br_y = interpolate(y_top_next, y_bottom_next, (row + 1) / total_rows)

    return int(cell_tl_x), int(cell_tl_y), int(cell_br_x), int(cell_br_y)

def capture_and_decode_inventory():
    """Captures the screen, detects items, maps them to the grid, and saves the layout."""
    print(f"Hotkey '{HOTKEY}' pressed. Capturing opponent inventory...")

    # 1. Load calibration and class data
    grid_coords = load_json(COORDINATES_FILE)
    with open(CLASSES_FILE, 'r') as f:
        class_names = [line.strip() for line in f]

    # 2. Take a screenshot
    with mss.mss() as sct:
        monitor = sct.monitors[1] # Assumes the primary monitor
        screenshot = sct.grab(monitor)
        img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")

    # 3. Crop the inventory region
    # Get the overall bounding box of the grid
    tl_x, tl_y = grid_coords['top_left']
    br_x, br_y = grid_coords['bottom_right']
    inventory_img = img.crop((tl_x, tl_y, br_x, br_y))

    # 4. Run YOLO detection
    results = model(inventory_img, verbose=False)

    # 5. Decode and map items to the grid
    grid = [[None for _ in range(GRID_DIMS[0])] for _ in range(GRID_DIMS[1])]
    detected_items = []

    for r in results:
        for box in r.boxes:
            class_id = int(box.cls[0])
            item_name = class_names[class_id]
            x1, y1, x2, y2 = [int(c) for c in box.xyxy[0]]
            center_x, center_y = (x1 + x2) / 2, (y1 + y2) / 2
            detected_items.append({'name': item_name, 'center': (center_x, center_y)})

    # Map detected item centers to grid cells
    for item in detected_items:
        item_center_x_abs = item['center'][0] + tl_x
        item_center_y_abs = item['center'][1] + tl_y

        for r in range(GRID_DIMS[1]):
            for c in range(GRID_DIMS[0]):
                cell_x1, cell_y1, cell_x2, cell_y2 = get_grid_cell_bounds(grid_coords, r, c, GRID_DIMS[1], GRID_DIMS[0])
                if cell_x1 <= item_center_x_abs <= cell_x2 and cell_y1 <= item_center_y_abs <= cell_y2:
                    if grid[r][c] is None:
                        grid[r][c] = item['name']
                    else:
                        print(f"Warning: Collision detected at grid cell ({r},{c}). Overwriting '{grid[r][c]}' with '{item['name']}'.")
                    break
            else:
                continue
            break

    # 6. Save the result
    output_data = {
        'timestamp': time.time(),
        'grid_layout': grid
    }
    
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        
    file_name = f"{uuid.uuid4()}.json"
    output_path = os.path.join(OUTPUT_DIR, file_name)
    
    with open(output_path, 'w') as f:
        json.dump(output_data, f, indent=4)
        
    print(f"Successfully saved opponent inventory layout to {output_path}")

# --- Main Execution ---
if __name__ == "__main__":
    print("Loading YOLO model...")
    model = YOLO(MODEL_PATH)
    print("Model loaded. Waiting for hotkey...")

    keyboard.add_hotkey(HOTKEY, capture_and_decode_inventory)
    print(f"Press '{HOTKEY}' to capture the opponent's inventory. Press 'esc' to exit.")
    keyboard.wait('esc')
    print("Exiting script.")
