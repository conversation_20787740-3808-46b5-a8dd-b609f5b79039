"""
Enhanced Combat System for Backpack Battles Simulation

Integrates all the new systems:
- Event-driven combat with priority queue
- Status effects system
- Resource management (stamina/mana)
- 6-stage damage pipeline
- Proper timing and effect ordering
"""

import random
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from simulator.combat_events import CombatSimulationEngine, CombatEvent, EventType
from simulator.status_effects import StatusEffectManager
from simulator.resources import ResourceManager
from simulator.damage_system import DamageCalculator, AttackInfo, DefenseInfo, DamageResult
from simulator.core import Player, ItemInstance


@dataclass
class CombatPlayer:
    """Enhanced player representation for combat"""
    player_id: int
    health: int
    max_health: int
    block: int = 0
    
    # Systems
    status_effects: StatusEffectManager = None
    resources: ResourceManager = None
    
    # Items
    items: List[ItemInstance] = None
    
    def __post_init__(self):
        if self.status_effects is None:
            self.status_effects = StatusEffectManager()
        if self.resources is None:
            self.resources = ResourceManager()
        if self.items is None:
            self.items = []
    
    def is_alive(self) -> bool:
        return self.health > 0
    
    def take_damage(self, amount: int) -> int:
        """Apply damage and return actual damage taken"""
        actual_damage = min(amount, self.health)
        self.health = max(0, self.health - amount)
        return actual_damage
    
    def heal(self, amount: int) -> int:
        """Apply healing and return actual healing done"""
        actual_healing = min(amount, self.max_health - self.health)
        self.health = min(self.max_health, self.health + amount)
        return actual_healing
    
    def consume_block(self, amount: int) -> int:
        """Consume block and return amount consumed"""
        consumed = min(amount, self.block)
        self.block = max(0, self.block - amount)
        return consumed
    
    def add_block(self, amount: int) -> None:
        """Add block value"""
        self.block += amount


class EnhancedCombatSystem:
    """Main combat system integrating all components"""
    
    def __init__(self):
        self.simulation_engine = CombatSimulationEngine()
        self.damage_calculator = DamageCalculator()
        self.battle_log: List[str] = []
        
        # Combat state
        self.players: List[CombatPlayer] = []
        self.current_time = 0.0
        self.battle_ended = False
        self.winner = None
        
    def initialize_battle(self, player1: Player, player2: Player) -> None:
        """Initialize a battle between two players"""
        self.battle_log.clear()
        self.damage_calculator.clear_log()
        self.battle_ended = False
        self.winner = None
        self.current_time = 0.0
        
        # Create combat players
        self.players = [
            CombatPlayer(
                player_id=0,
                health=player1.health,
                max_health=player1.health,
                items=list(player1.backpack.items.values())
            ),
            CombatPlayer(
                player_id=1,
                health=player2.health,
                max_health=player2.health,
                items=list(player2.backpack.items.values())
            )
        ]
        
        # Prepare items for simulation
        player1_items = self._prepare_items_for_simulation(self.players[0])
        player2_items = self._prepare_items_for_simulation(self.players[1])
        
        # Initialize the simulation engine
        self.simulation_engine.initialize_battle(player1_items, player2_items)
        
        self.battle_log.append("=== BATTLE INITIALIZED ===")
        self.battle_log.append(f"Player 1: {player1.health} HP, {len(player1_items)} items")
        self.battle_log.append(f"Player 2: {player2.health} HP, {len(player2_items)} items")
    
    def _prepare_items_for_simulation(self, player: CombatPlayer) -> List[Dict[str, Any]]:
        """Convert player items to simulation format"""
        items = []
        
        for item_instance in player.items:
            item_data = {
                'item_id': str(item_instance.instance_id),
                'player': player.player_id,
                'name': item_instance.item.name,
                'cooldown': getattr(item_instance.item, 'cooldown', 0),
                'has_start_effect': self._has_start_of_battle_effect(item_instance),
                'activation_callback': lambda event: self._handle_item_activation(event)
            }
            
            # Add item-specific data
            if hasattr(item_instance.item, 'raw_stats'):
                item_data.update(item_instance.item.raw_stats)
            
            items.append(item_data)
        
        return items
    
    def _has_start_of_battle_effect(self, item_instance: ItemInstance) -> bool:
        """Check if item has start-of-battle effects"""
        # This would check the item's effects for start-of-battle triggers
        # For now, return False as placeholder
        return False
    
    def run_battle(self) -> Dict[str, Any]:
        """Run the complete battle simulation"""
        self.battle_log.append("=== BATTLE START ===")
        
        def battle_event_handler(event: CombatEvent) -> bool:
            """Handle each combat event"""
            self.current_time = event.time
            
            # Update all player systems
            for player in self.players:
                player.status_effects.update(self.current_time, 0.1)
                player.resources.update(self.current_time, player.status_effects)
            
            # Process the event
            continue_battle = self._process_combat_event(event)
            
            # Check for battle end conditions
            if not continue_battle or self._check_battle_end():
                return False
            
            return True
        
        # Run the simulation
        result = self.simulation_engine.run_simulation(battle_event_handler)
        
        # Finalize battle results
        self._finalize_battle_results(result)
        
        return result
    
    def _process_combat_event(self, event: CombatEvent) -> bool:
        """Process a single combat event"""
        try:
            if event.event_type == EventType.START_OF_BATTLE:
                return self._handle_start_of_battle(event)
            elif event.event_type == EventType.ITEM_ACTIVATION:
                return self._handle_item_activation(event)
            elif event.event_type == EventType.STATUS_TICK:
                return self._handle_status_tick(event)
            elif event.event_type == EventType.STATUS_EXPIRE:
                return self._handle_status_expire(event)
            elif event.event_type == EventType.FATIGUE_DAMAGE:
                return self._handle_fatigue_damage(event)
            else:
                self.battle_log.append(f"Unknown event type: {event.event_type}")
                return True
                
        except Exception as e:
            self.battle_log.append(f"Error processing event: {e}")
            return False
    
    def _handle_start_of_battle(self, event: CombatEvent) -> bool:
        """Handle start-of-battle effects"""
        self.battle_log.append(f"Start-of-battle effect: {event.source_id}")
        
        # Process start-of-battle effects based on item
        # This would be implemented based on specific item effects
        
        return True
    
    def _handle_item_activation(self, event: CombatEvent) -> bool:
        """Handle item activation"""
        player = self.players[event.target_player]
        
        # Find the item
        item_instance = None
        for item in player.items:
            if str(item.instance_id) == event.source_id:
                item_instance = item
                break
        
        if not item_instance:
            return True
        
        self.battle_log.append(f"Player {event.target_player + 1}: {item_instance.item.name} activates")
        
        # Check stamina requirements
        stamina_cost = getattr(item_instance.item, 'stamina_cost', 0)
        if stamina_cost > 0:
            if not player.resources.use_stamina(stamina_cost, event.source_id):
                self.battle_log.append(f"  -> Insufficient stamina ({stamina_cost} needed)")
                return True
        
        # Process item effect (simplified for now)
        self._process_item_effect(item_instance, player, event)
        
        # Reschedule next activation
        cooldown = getattr(item_instance.item, 'cooldown', 0)
        if cooldown > 0:
            # Apply status effect modifiers to cooldown
            cooldown_modifier = player.status_effects.get_stat_modifier('cooldown_multiplier')
            effective_cooldown = cooldown * cooldown_modifier
            
            self.simulation_engine.event_manager.reschedule_item_activation(
                event.source_id,
                effective_cooldown,
                event.data,
                lambda e: self._handle_item_activation(e)
            )
        
        return True
    
    def _process_item_effect(self, item: ItemInstance, player: CombatPlayer, event: CombatEvent) -> None:
        """Process the effect of an item activation"""
        # This is a simplified implementation
        # Real implementation would parse item effects and apply them
        
        # Example: If it's a weapon, deal damage
        if hasattr(item.item, 'damage_min') and hasattr(item.item, 'damage_max'):
            self._process_weapon_attack(item, player)
    
    def _process_weapon_attack(self, weapon: ItemInstance, attacker: CombatPlayer) -> None:
        """Process a weapon attack"""
        target_player = self.players[1 - attacker.player_id]  # Other player
        
        # Create attack info
        attack = AttackInfo(
            min_damage=getattr(weapon.item, 'damage_min', 1),
            max_damage=getattr(weapon.item, 'damage_max', 1),
            accuracy=getattr(weapon.item, 'accuracy', 0.9),
            crit_chance=0.0,  # Would be calculated from player effects
            damage_type=getattr(weapon.item, 'damage_type', 'physical')
        )
        
        # Create defense info
        defense = DefenseInfo(
            current_health=target_player.health,
            max_health=target_player.max_health,
            block=target_player.block,
            status_effects={name: effect.stacks for name, effect in target_player.status_effects.effects.items()}
        )
        
        # Calculate damage
        attacker_effects = {name: effect.stacks for name, effect in attacker.status_effects.effects.items()}
        damage_result = self.damage_calculator.calculate_damage(attack, attacker_effects, defense)
        
        # Apply damage
        if damage_result.was_hit and damage_result.final_damage > 0:
            actual_damage = target_player.take_damage(damage_result.final_damage)
            target_player.consume_block(damage_result.block_consumed)
            
            self.battle_log.append(f"  -> Deals {actual_damage} damage to Player {target_player.player_id + 1}")
            
            # Process reflected damage (Spikes)
            if target_player.status_effects.has_effect("Spikes"):
                reflected = self.damage_calculator.calculate_reflected_damage(
                    damage_result.final_damage,
                    {name: effect.stacks for name, effect in target_player.status_effects.effects.items()}
                )
                if reflected > 0:
                    attacker.take_damage(reflected)
                    self.battle_log.append(f"  -> Spikes reflect {reflected} damage")
        
        # Process on-hit effects
        for effect in damage_result.effects_triggered:
            if effect.startswith("Vampirism:"):
                heal_amount = int(effect.split(":")[1])
                actual_heal = attacker.heal(heal_amount)
                self.battle_log.append(f"  -> Vampirism heals {actual_heal}")
    
    def _handle_status_tick(self, event: CombatEvent) -> bool:
        """Handle status effect ticks (DoT/HoT)"""
        player = self.players[event.target_player]
        effect_name = event.data.get("effect_name", "")
        
        if effect_name == "Poison":
            stacks = player.status_effects.get_effect_stacks("Poison")
            if stacks > 0:
                damage = self.damage_calculator.calculate_poison_damage(stacks)
                actual_damage = player.take_damage(damage)
                self.battle_log.append(f"Player {event.target_player + 1}: Poison deals {actual_damage} damage")
                
                # Reschedule next tick
                self.simulation_engine.event_manager.schedule_status_tick(effect_name, event.target_player)
        
        elif effect_name == "Regeneration":
            stacks = player.status_effects.get_effect_stacks("Regeneration")
            if stacks > 0:
                healing = self.damage_calculator.calculate_regeneration_healing(stacks)
                actual_heal = player.heal(healing)
                self.battle_log.append(f"Player {event.target_player + 1}: Regeneration heals {actual_heal}")
                
                # Reschedule next tick
                self.simulation_engine.event_manager.schedule_status_tick(effect_name, event.target_player)
        
        return True
    
    def _handle_status_expire(self, event: CombatEvent) -> bool:
        """Handle status effect expiration"""
        player = self.players[event.target_player]
        effect_name = event.data.get("effect_name", "")
        
        player.status_effects.remove_effect(effect_name)
        self.battle_log.append(f"Player {event.target_player + 1}: {effect_name} expires")
        
        return True
    
    def _handle_fatigue_damage(self, event: CombatEvent) -> bool:
        """Handle fatigue damage to prevent infinite battles"""
        damage = event.data.get("damage", 1)
        
        for player in self.players:
            actual_damage = player.take_damage(damage)
            self.battle_log.append(f"Player {player.player_id + 1}: Fatigue deals {actual_damage} damage")
        
        # Schedule next fatigue damage with increased amount
        next_damage = damage + 1
        self.simulation_engine.event_manager.schedule_fatigue_damage(self.current_time + 5.0)
        
        return True
    
    def _check_battle_end(self) -> bool:
        """Check if the battle should end"""
        alive_players = [p for p in self.players if p.is_alive()]
        
        if len(alive_players) <= 1:
            self.battle_ended = True
            if len(alive_players) == 1:
                self.winner = alive_players[0].player_id
                self.battle_log.append(f"=== BATTLE END: Player {self.winner + 1} wins! ===")
            else:
                self.winner = None
                self.battle_log.append("=== BATTLE END: Draw! ===")
            return True
        
        return False
    
    def _finalize_battle_results(self, simulation_result: Dict[str, Any]) -> None:
        """Finalize battle results"""
        simulation_result.update({
            "winner": self.winner,
            "player1_health": self.players[0].health,
            "player2_health": self.players[1].health,
            "battle_log": self.battle_log.copy(),
            "damage_log": self.damage_calculator.get_damage_log()
        })
    
    def get_battle_state(self) -> Dict[str, Any]:
        """Get current battle state"""
        return {
            "time": self.current_time,
            "players": [
                {
                    "health": p.health,
                    "max_health": p.max_health,
                    "block": p.block,
                    "status_effects": {name: effect.stacks for name, effect in p.status_effects.effects.items()},
                    "resources": p.resources.to_dict()
                }
                for p in self.players
            ],
            "battle_ended": self.battle_ended,
            "winner": self.winner
        }
