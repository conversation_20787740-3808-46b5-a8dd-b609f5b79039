# Project Progress

This file tracks the progress of the project to simulate the game and train an RL AI.

## Task 1: Analyze DeepResearch.txt and Update Systems

*   [X] Read and analyze `DeepResearch.txt`
*   [ ] Implement changes based on the research.

### Section 1: Foundational Game Architecture
- [X] **Game Loop (Finite State Machine):** Verify the main game loop implements the Shop -> Battle -> Combination -> Shop state machine.
- [X] **Player State Vector:** Verify the correct representation of player state (Health, Gold, Stamina, Mana, Lives, Trophies, Class, Subclass, Rank, CurrentRound, Inventory).
- [ ] **Backpack Grid System:** Verify the implementation of the backpack grid, including expansion with bags, item rotation, and synergy connectors (★ and ♦).
- [X] **Game Progression and Termination:** Verify resource progression (Health and Gold per round) and termination conditions (0 lives, 10 trophies, or round 18).

### Section 2: The Shop Phase
- [X] **Shop State and Action Space:** Verify the shop implementation (items, reroll cost, actions: Purchase, Sell, Reroll, Reserve, ToggleCombinationLock).
- [X] **Item Generation Algorithm:** Implement rarity-based weighted sampling that changes with the round, and item pool filtering (class, subclass, unique items).
- [X] **Shop Sub-systems and Pity Timers:** Implement pity timers for sales and bags. Add a `use_shop_blacklist` parameter and set it to `False`.

### Section 3: The Combat Phase
- [X] **Combat Initialization and "Start of Battle" Effects:** Implement the "First-In, First-Out" (FIFO) or "Insertion Order" priority system for "Start of Battle" effects based on item placement order.
- [X] **Action & Event Queue:** Refactor the combat loop to use a priority queue for continuous-time event simulation.
- [ ] **Resource Dynamics (Stamina and Mana):** Verify the implementation of the stamina and mana models, especially the "Out of Stamina" state.
- [ ] **Damage Calculation and Resolution:** Verify the damage calculation pipeline (Base Damage, Crit Check, Accuracy Check, Block Application, Health Reduction, "On Hit" effects).
- [ ] **Status Effects:** Implement all status effects as per the research.

### Section 4: The Item Compendium
- [ ] **Item Data Schema:** Ensure the item data structure matches the schema in the research.
- [ ] **Databases:** Create separate databases for neutral and class-specific items.
- [ ] **Gemstone Mechanics:** Implement the context-dependent effects of gemstones.

### Section 5: Character Archetypes and Specializations
- [ ] **Base Class Definitions:** Implement the starting bags and their effects for each class.
- [ ] **Subclass System:** Implement the subclass system, including the special shop at round 8 and unlocking new items.

### Section 6: The Combination System
- [ ] **Recipe Activation Logic:** Verify the recipe activation logic (adjacency, catalysts, combination lock).

### Section 7: Opponent Simulation Model
- [ ] **Asynchronous Matchmaking:** Ensure the simulation uses "ghost" opponents.
- [ ] **Opponent Build Progression and Selection Bias:** Implement a system for generating or loading opponents that get stronger as the rounds progress.
- [ ] **Rank-Based Difficulty Scaling:** The opponent model should take the player's rank into account.
