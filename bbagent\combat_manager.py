import heapq

class CombatManager:
    """
    Manages the combat phase of the game, simulating the battle between the player and an opponent.
    """
    def __init__(self, player_inventory, opponent_inventory):
        self.player_inventory = player_inventory
        self.opponent_inventory = opponent_inventory
        self.event_queue = []
        self.time = 0.0

    def simulate_battle(self):
        """
        Simulates the entire battle and returns the outcome.
        """
        # 1. Initialization (Start of Battle effects)
        self.initialize_battle()

        # 2. Main combat loop
        while self.player_inventory.health > 0 and self.opponent_inventory.health > 0:
            if not self.event_queue:
                break

            # Get the next event
            self.time, event = heapq.heappop(self.event_queue)

            # Execute the event
            event.execute()

            # Re-queue periodic events
            if event.is_periodic:
                heapq.heappush(self.event_queue, (self.time + event.cooldown, event))

        # 3. Determine outcome
        if self.player_inventory.health <= 0:
            return "loss"
        elif self.opponent_inventory.health <= 0:
            return "win"
        else:
            return "draw" # Or handle fatigue

    def initialize_battle(self):
        """
        Initializes the battle by processing all "Start of Battle" effects.
        """
        # Get all items with "Start of Battle" effects from both inventories
        player_start_of_battle_items = sorted([item for item in self.player_inventory.items if hasattr(item, 'on_start_of_battle')], key=lambda x: x.placement_order)
        opponent_start_of_battle_items = sorted([item for item in self.opponent_inventory.items if hasattr(item, 'on_start_of_battle')], key=lambda x: x.placement_order)

        # Execute effects in placement order
        for item in player_start_of_battle_items:
            item.on_start_of_battle(self.player_inventory, self.opponent_inventory)
        for item in opponent_start_of_battle_items:
            item.on_start_of_battle(self.opponent_inventory, self.player_inventory)

        # Populate the event queue with initial periodic events
        for item in self.player_inventory.items:
            if hasattr(item, 'cooldown'):
                heapq.heappush(self.event_queue, (self.time + item.cooldown, item.event))
        for item in self.opponent_inventory.items:
            if hasattr(item, 'cooldown'):
                heapq.heappush(self.event_queue, (self.time + item.cooldown, item.event))