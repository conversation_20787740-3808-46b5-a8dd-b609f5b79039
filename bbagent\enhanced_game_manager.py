"""
Enhanced Game Manager for Backpack Battles Simulation

Integrates all the new research-compliant systems:
- Enhanced shop with pity timers
- Event-driven combat system
- Status effects and resource management
- Proper game state management
"""

from time import sleep
from typing import Dict, List, Optional, Any

# Import existing systems
from .inventory_manager import InventoryManager
from .recipe_manager import RecipeManager
from .utils import load_item_data, load_recipe_data
from .item import Item

# Import new enhanced systems
from simulator.enhanced_shop import EnhancedShop
from simulator.enhanced_combat import EnhancedCombatSystem
from simulator.status_effects import StatusEffectManager
from simulator.resources import ResourceManager
from simulator.core import Player, Backpack


class EnhancedGameManager:
    """
    Enhanced Game Manager integrating all research-compliant systems.
    Provides accurate simulation for RL training and assist mode.
    """
    
    def __init__(self, player_class: str = "Ranger"):
        """Initialize the enhanced game manager"""
        
        # Load game data
        self.item_data = load_item_data()
        recipes_data = load_recipe_data().get('recipes', [])
        if not recipes_data:
            raise ValueError("No recipes found in data/recipes.json or file is empty.")
        
        # Initialize managers
        self.inventory_manager = InventoryManager()
        self.recipe_manager = RecipeManager(recipes_data)
        
        # Initialize enhanced systems
        self.shop = EnhancedShop(self.item_data, use_shop_blacklist=False)
        self.combat_system = EnhancedCombatSystem()
        self.status_effects = StatusEffectManager()
        self.resources = ResourceManager()
        
        # Game state tracking
        self.current_inventory_items = []
        
        # Resource schedule from Table 1.1 (DeepResearch.txt)
        self.resource_schedule = {
            1: {'gold': 12, 'max_health': 25}, 2: {'gold': 9, 'max_health': 35},
            3: {'gold': 9, 'max_health': 45}, 4: {'gold': 9, 'max_health': 55},
            5: {'gold': 10, 'max_health': 70}, 6: {'gold': 10, 'max_health': 85},
            7: {'gold': 11, 'max_health': 100}, 8: {'gold': 21, 'max_health': 115},
            9: {'gold': 12, 'max_health': 130}, 10: {'gold': 12, 'max_health': 150},
            11: {'gold': 13, 'max_health': 170}, 12: {'gold': 13, 'max_health': 190},
            13: {'gold': 14, 'max_health': 210}, 14: {'gold': 14, 'max_health': 230},
            15: {'gold': 15, 'max_health': 260}, 16: {'gold': 15, 'max_health': 290},
            17: {'gold': 15, 'max_health': 320}, 18: {'gold': 15, 'max_health': 350}
        }
        
        # Player state vector (Section 1.2)
        self.current_round = 1
        self.health = self.resource_schedule[self.current_round]['max_health']
        self.max_health = self.health
        self.gold = self.resource_schedule[self.current_round]['gold']
        self.lives = 5
        self.trophies = 0
        
        # Class and progression
        self.player_class = player_class
        self.subclass = None
        self.rank = "Bronze"
        
        # Game state machine (Section 1.1)
        self.game_state = 'shop_phase'  # shop_phase, battle_phase, combination_phase
        
        # Battle tracking
        self.last_battle_result = None
        self.battle_log = []
        
        # Initialize shop for first round
        self.shop.generate_shop_offerings(
            self.current_round, 
            self.player_class, 
            self.subclass, 
            force_new_round=True
        )
    
    def get_game_state(self) -> Dict[str, Any]:
        """Get comprehensive game state"""
        return {
            # Core state
            "round": self.current_round,
            "health": self.health,
            "max_health": self.max_health,
            "gold": self.gold,
            "lives": self.lives,
            "trophies": self.trophies,
            "game_phase": self.game_state,
            
            # Player info
            "player_class": self.player_class,
            "subclass": self.subclass,
            "rank": self.rank,
            
            # Systems state
            "status_effects": {
                name: effect.stacks 
                for name, effect in self.status_effects.effects.items()
            },
            "resources": self.resources.to_dict(),
            "shop_state": self.shop.get_shop_state(),
            
            # Inventory
            "inventory_items": len(self.current_inventory_items),
            "placed_items": len(self.inventory_manager.placed_items),
            
            # Battle info
            "last_battle_result": self.last_battle_result
        }
    
    def update_inventory_state(self, new_items_data: List[Dict]) -> None:
        """Update inventory with new items from vision system"""
        self.inventory_manager.clear()
        self.current_inventory_items = []
        
        for item_info in new_items_data:
            item_name = item_info.get("name")
            item_details = self.item_data.get("items", {}).get(item_name)
            if item_details:
                item = Item(
                    name=item_name,
                    grid_layout=item_details.get("grid_layout", []),
                    grid_size=item_details.get("grid_size", (0, 0)),
                    item_type=item_details.get("type", "other"),
                    rarity=item_details.get("rarity", "Common"),
                    item_class=item_details.get("class", "Neutral"),
                    subtype=item_details.get("subtype", []),
                    cost=item_details.get("cost", 0),
                    position=item_info.get("position"),
                    current_rotation=item_info.get("rotation", 0)
                )
                self.current_inventory_items.append(item)
    
    def run_game_loop(self) -> None:
        """Main game loop implementing the finite state machine"""
        print("Enhanced Game Manager starting...")
        
        while self.lives > 0 and self.current_round <= 18:
            if self.game_state == 'shop_phase':
                self.handle_shop_phase()
            elif self.game_state == 'battle_phase':
                self.handle_battle_phase()
            elif self.game_state == 'combination_phase':
                self.handle_combination_phase()
            
            # Check for termination conditions
            if self.trophies >= 10:
                print("Victory! 10 trophies achieved.")
                break
            
            sleep(0.1)  # Small delay for real-time operation
    
    def handle_shop_phase(self) -> None:
        """Handle the shop phase with enhanced shop system"""
        print(f"=== Round {self.current_round} - Shop Phase ===")
        print(f"Gold: {self.gold}, Health: {self.health}, Lives: {self.lives}, Trophies: {self.trophies}")
        
        # Display shop offerings
        shop_state = self.shop.get_shop_state()
        print("Shop offerings:")
        for i, (item_name, cost, original_cost) in enumerate(zip(
            shop_state["offerings"], 
            shop_state["costs"], 
            shop_state["original_costs"]
        )):
            if item_name:
                sale_text = f" (SALE! was {original_cost})" if cost < original_cost else ""
                lock_text = " [LOCKED]" if shop_state["locks"][i] else ""
                print(f"  {i+1}. {item_name} - {cost}g{sale_text}{lock_text}")
            else:
                print(f"  {i+1}. [Empty]")
        
        print(f"Reroll cost: {shop_state['reroll_cost']}g")
        
        # For now, automatically proceed to battle
        # In a real implementation, this would wait for player/agent actions
        print("Proceeding to battle...")
        self.start_battle()
    
    def handle_battle_phase(self) -> None:
        """Handle battle phase with enhanced combat system"""
        print("=== Battle Phase ===")
        
        # Create player representation for combat
        player_backpack = Backpack()
        # Add current inventory items to backpack
        # (This would need proper conversion from current inventory system)
        
        player = Player(
            name="Agent",
            player_class=self.player_class,
            health=self.health,
            gold=self.gold
        )
        player.backpack = player_backpack
        
        # Generate opponent (simplified for now)
        opponent = Player(
            name="Opponent",
            player_class="Ranger",  # Placeholder
            health=self.health,  # Same health for now
            gold=0
        )
        
        # Run enhanced combat simulation
        self.combat_system.initialize_battle(player, opponent)
        battle_result = self.combat_system.run_battle()
        
        # Process battle results
        self.last_battle_result = battle_result
        self.battle_log = battle_result.get("battle_log", [])
        
        if battle_result.get("winner") == 0:  # Player won
            print("Battle won!")
            self.trophies += 1
        else:
            print("Battle lost!")
            self.lives -= 1
        
        # Update health based on battle
        self.health = battle_result.get("player1_health", self.health)
        
        self.game_state = 'combination_phase'
    
    def handle_combination_phase(self) -> None:
        """Handle combination phase"""
        print("=== Combination Phase ===")
        
        # Check for valid recipes
        recipe_to_craft = self.recipe_manager.check_for_combinations(self.inventory_manager)
        if recipe_to_craft:
            print(f"Found valid recipe: {recipe_to_craft.result}")
            self.execute_combination(recipe_to_craft)
        
        # Advance to next round
        self.advance_to_next_round()
    
    def advance_to_next_round(self) -> None:
        """Advance to the next round"""
        self.current_round += 1
        
        if self.current_round <= 18:
            # Update resources based on schedule
            round_data = self.resource_schedule[self.current_round]
            self.gold += round_data['gold']  # Add gold for new round
            self.max_health = round_data['max_health']
            self.health = self.max_health  # Full heal each round
            
            # Reset resources for new round
            self.resources.reset()
            
            # Generate new shop offerings
            self.shop.generate_shop_offerings(
                self.current_round,
                self.player_class,
                self.subclass,
                force_new_round=True
            )
            
            # Check for subclass selection at Round 8
            if self.current_round == 8 and self.subclass is None:
                print("Subclass selection available!")
                # In a real implementation, this would present subclass choices
            
            self.game_state = 'shop_phase'
        else:
            print("Game completed - Round 18 finished!")
    
    def start_battle(self) -> None:
        """Transition to battle phase"""
        self.game_state = 'battle_phase'
    
    def execute_combination(self, recipe) -> None:
        """Execute a recipe combination"""
        print(f"Executing combination: {recipe.result}")
        # Implementation would handle the actual combination logic
    
    def buy_shop_item(self, slot_index: int) -> bool:
        """Buy an item from the shop"""
        player = Player(
            name="Agent",
            player_class=self.player_class,
            health=self.health,
            gold=self.gold
        )
        
        success = self.shop.buy_item(player, slot_index)
        if success:
            self.gold = player.gold
            print(f"Purchased item from slot {slot_index + 1}")
        
        return success
    
    def reroll_shop(self) -> bool:
        """Reroll the shop"""
        player = Player(
            name="Agent",
            player_class=self.player_class,
            health=self.health,
            gold=self.gold
        )
        
        success = self.shop.reroll_shop(player, self.current_round, self.player_class, self.subclass)
        if success:
            self.gold = player.gold
            print("Shop rerolled")
        
        return success
    
    def toggle_shop_lock(self, slot_index: int) -> bool:
        """Toggle lock on a shop slot"""
        return self.shop.toggle_lock(slot_index)
    
    def get_battle_statistics(self) -> Dict[str, Any]:
        """Get detailed battle statistics"""
        if not self.last_battle_result:
            return {}
        
        return {
            "battle_time": self.last_battle_result.get("battle_time", 0),
            "events_processed": self.last_battle_result.get("events_processed", 0),
            "simulation_time": self.last_battle_result.get("simulation_time", 0),
            "winner": self.last_battle_result.get("winner"),
            "damage_log": self.last_battle_result.get("damage_log", []),
            "battle_log": self.battle_log
        }
    
    def get_shop_statistics(self) -> Dict[str, Any]:
        """Get shop statistics including pity timers"""
        shop_state = self.shop.get_shop_state()
        return {
            "current_offerings": shop_state["offerings"],
            "reroll_cost": shop_state["reroll_cost"],
            "rerolls_this_round": shop_state["rerolls_this_round"],
            "pity_timers": shop_state["pity_timers"],
            "unique_item_owned": shop_state["unique_item_owned"]
        }
