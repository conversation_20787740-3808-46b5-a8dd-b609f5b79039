"""
Damage Calculation System for Backpack Battles Simulation

Implements the 6-stage damage pipeline as defined in DeepResearch.txt Section 3.4:
1. Base Damage Calculation
2. Critical Hit Check  
3. Accuracy Check
4. Block Application
5. Health Reduction
6. "On Hit" Effect Triggering
"""

import random
import math
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum


class DamageType(Enum):
    """Types of damage"""
    PHYSICAL = "physical"
    MAGIC = "magic"
    TRUE = "true"  # Bypasses block
    POISON = "poison"  # DoT damage


@dataclass
class DamageResult:
    """Result of a damage calculation"""
    raw_damage: int
    final_damage: int
    was_critical: bool
    was_hit: bool
    was_blocked: bool
    block_consumed: int
    effects_triggered: List[str]
    damage_type: DamageType = DamageType.PHYSICAL


@dataclass
class AttackInfo:
    """Information about an attack"""
    min_damage: int
    max_damage: int
    accuracy: float  # Base accuracy (0.0 to 1.0)
    crit_chance: float = 0.0
    crit_multiplier: float = 2.0
    damage_type: DamageType = DamageType.PHYSICAL
    on_hit_effects: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.on_hit_effects is None:
            self.on_hit_effects = []


@dataclass
class DefenseInfo:
    """Information about a defender"""
    current_health: int
    max_health: int
    block: int
    status_effects: Dict[str, int]  # effect_name -> stacks
    
    def has_effect(self, effect_name: str) -> bool:
        return effect_name in self.status_effects and self.status_effects[effect_name] > 0
    
    def get_effect_stacks(self, effect_name: str) -> int:
        return self.status_effects.get(effect_name, 0)


class DamageCalculator:
    """Handles all damage calculations using the 6-stage pipeline"""
    
    def __init__(self):
        self.damage_log: List[str] = []
        
    def calculate_damage(self, attack: AttackInfo, attacker_effects: Dict[str, int],
                        defender: DefenseInfo) -> DamageResult:
        """
        Execute the complete 6-stage damage pipeline
        
        Args:
            attack: Attack information
            attacker_effects: Attacker's status effects
            defender: Defender information
            
        Returns:
            DamageResult with all calculation details
        """
        result = DamageResult(
            raw_damage=0,
            final_damage=0,
            was_critical=False,
            was_hit=False,
            was_blocked=False,
            block_consumed=0,
            effects_triggered=[],
            damage_type=attack.damage_type
        )
        
        # Stage 1: Base Damage Calculation
        result.raw_damage = self._calculate_base_damage(attack, attacker_effects)
        
        # Stage 2: Critical Hit Check
        result.was_critical, crit_damage = self._check_critical_hit(
            result.raw_damage, attack, attacker_effects
        )
        modified_damage = crit_damage
        
        # Stage 3: Accuracy Check
        result.was_hit = self._check_accuracy(attack, attacker_effects, defender)
        if not result.was_hit:
            result.final_damage = 0
            self.damage_log.append(f"Attack missed! (Accuracy check failed)")
            return result
        
        # Stage 4: Block Application
        if attack.damage_type != DamageType.TRUE and attack.damage_type != DamageType.POISON:
            result.final_damage, result.block_consumed = self._apply_block(
                modified_damage, defender.block
            )
            result.was_blocked = result.block_consumed > 0
        else:
            result.final_damage = modified_damage
            result.block_consumed = 0
        
        # Stage 5: Health Reduction (handled by caller)
        # This stage is handled by the combat system that calls this function
        
        # Stage 6: "On Hit" Effect Triggering
        if result.was_hit:
            result.effects_triggered = self._trigger_on_hit_effects(
                attack, attacker_effects, defender, result
            )
        
        self._log_damage_calculation(attack, result)
        return result
    
    def _calculate_base_damage(self, attack: AttackInfo, attacker_effects: Dict[str, int]) -> int:
        """Stage 1: Calculate base damage with Empower stacks"""
        base_damage = random.randint(attack.min_damage, attack.max_damage)
        empower_bonus = attacker_effects.get("Empower", 0)
        
        raw_damage = base_damage + empower_bonus
        self.damage_log.append(f"Base damage: {base_damage} + {empower_bonus} Empower = {raw_damage}")
        
        return raw_damage
    
    def _check_critical_hit(self, base_damage: int, attack: AttackInfo, 
                          attacker_effects: Dict[str, int]) -> Tuple[bool, int]:
        """Stage 2: Check for critical hit"""
        # Calculate total crit chance
        base_crit = attack.crit_chance
        luck_bonus = attacker_effects.get("Luck", 0) * 0.05  # 5% per Luck stack
        total_crit_chance = base_crit + luck_bonus
        
        is_crit = random.random() < total_crit_chance
        
        if is_crit:
            crit_damage = int(base_damage * attack.crit_multiplier)
            self.damage_log.append(f"CRITICAL HIT! {base_damage} × {attack.crit_multiplier} = {crit_damage}")
            return True, crit_damage
        else:
            return False, base_damage
    
    def _check_accuracy(self, attack: AttackInfo, attacker_effects: Dict[str, int],
                       defender: DefenseInfo) -> bool:
        """Stage 3: Check if attack hits"""
        # Calculate final accuracy
        base_accuracy = attack.accuracy
        luck_modifier = 1.0 + (0.05 * attacker_effects.get("Luck", 0))
        blind_modifier = 1.0 - (0.05 * defender.get_effect_stacks("Blind"))
        
        final_accuracy = base_accuracy * luck_modifier * blind_modifier
        final_accuracy = max(0.0, min(1.0, final_accuracy))  # Clamp to 0-1
        
        hit = random.random() < final_accuracy
        
        self.damage_log.append(
            f"Accuracy: {base_accuracy:.0%} × {luck_modifier:.2f} × {blind_modifier:.2f} = {final_accuracy:.0%} -> {'HIT' if hit else 'MISS'}"
        )
        
        return hit
    
    def _apply_block(self, damage: int, block_value: int) -> Tuple[int, int]:
        """Stage 4: Apply block damage reduction"""
        if block_value <= 0:
            return damage, 0
        
        block_consumed = min(damage, block_value)
        final_damage = max(0, damage - block_value)
        
        self.damage_log.append(f"Block: {damage} - {block_consumed} = {final_damage}")
        
        return final_damage, block_consumed
    
    def _trigger_on_hit_effects(self, attack: AttackInfo, attacker_effects: Dict[str, int],
                              defender: DefenseInfo, damage_result: DamageResult) -> List[str]:
        """Stage 6: Trigger on-hit effects"""
        triggered_effects = []
        
        # Process weapon's on-hit effects
        for effect in attack.on_hit_effects:
            effect_name = effect.get("name", "")
            triggered_effects.append(effect_name)
            self.damage_log.append(f"On-hit effect triggered: {effect_name}")
        
        # Process attacker status effects that trigger on hit
        if attacker_effects.get("Vampirism", 0) > 0 and damage_result.final_damage > 0:
            vampirism_stacks = attacker_effects["Vampirism"]
            heal_amount = min(damage_result.final_damage, vampirism_stacks)
            triggered_effects.append(f"Vampirism:{heal_amount}")
            self.damage_log.append(f"Vampirism: Heal {heal_amount}")
        
        return triggered_effects
    
    def calculate_reflected_damage(self, incoming_damage: int, defender_effects: Dict[str, int]) -> int:
        """Calculate damage reflected by Spikes"""
        spikes_stacks = defender_effects.get("Spikes", 0)
        if spikes_stacks > 0:
            reflected = min(incoming_damage, spikes_stacks)
            self.damage_log.append(f"Spikes: Reflect {reflected} damage")
            return reflected
        return 0
    
    def calculate_poison_damage(self, poison_stacks: int) -> int:
        """Calculate poison damage (bypasses block)"""
        damage = poison_stacks  # 1 damage per stack
        if damage > 0:
            self.damage_log.append(f"Poison: {damage} damage (bypasses block)")
        return damage
    
    def calculate_regeneration_healing(self, regen_stacks: int) -> int:
        """Calculate regeneration healing"""
        healing = regen_stacks  # 1 healing per stack
        if healing > 0:
            self.damage_log.append(f"Regeneration: {healing} healing")
        return healing
    
    def _log_damage_calculation(self, attack: AttackInfo, result: DamageResult) -> None:
        """Log the complete damage calculation"""
        self.damage_log.append(
            f"Final result: {result.raw_damage} raw → {result.final_damage} final "
            f"(Crit: {result.was_critical}, Hit: {result.was_hit}, Blocked: {result.block_consumed})"
        )
    
    def get_damage_log(self) -> List[str]:
        """Get the damage calculation log"""
        return self.damage_log.copy()
    
    def clear_log(self) -> None:
        """Clear the damage calculation log"""
        self.damage_log.clear()


class CombatStatsCalculator:
    """Utility class for calculating combat statistics"""
    
    @staticmethod
    def calculate_effective_dps(weapon_damage: Tuple[int, int], cooldown: float,
                              accuracy: float, crit_chance: float, crit_mult: float = 2.0) -> float:
        """Calculate theoretical DPS of a weapon"""
        avg_damage = (weapon_damage[0] + weapon_damage[1]) / 2
        crit_multiplier = 1.0 + (crit_chance * (crit_mult - 1.0))
        effective_damage = avg_damage * accuracy * crit_multiplier
        
        if cooldown <= 0:
            return 0.0
        
        return effective_damage / cooldown
    
    @staticmethod
    def calculate_stamina_efficiency(weapon_damage: Tuple[int, int], stamina_cost: float,
                                   accuracy: float = 1.0) -> float:
        """Calculate damage per stamina point"""
        avg_damage = (weapon_damage[0] + weapon_damage[1]) / 2
        effective_damage = avg_damage * accuracy
        
        if stamina_cost <= 0:
            return float('inf')
        
        return effective_damage / stamina_cost
    
    @staticmethod
    def calculate_luck_value(base_accuracy: float, base_crit: float, luck_stacks: int) -> Dict[str, float]:
        """Calculate the value of Luck stacks"""
        accuracy_bonus = luck_stacks * 0.05
        crit_bonus = luck_stacks * 0.05
        
        return {
            "accuracy_increase": accuracy_bonus,
            "crit_increase": crit_bonus,
            "total_accuracy": min(1.0, base_accuracy + accuracy_bonus),
            "total_crit": min(1.0, base_crit + crit_bonus)
        }
