import tkinter as tk
from PIL import Image, ImageTk
import json

# --- CONFIGURATION ---
IMAGE_PATH = "data/screenshots/BattlePhase-Over.png"
OUTPUT_FILE = "opponent_grid_coordinates.json"
# ---------------------

clicks = []

def on_image_click(event):
    """Records the (x, y) coordinates of a click and provides visual feedback."""
    x, y = event.x, event.y
    clicks.append((x, y))
    
    # Draw a small red circle to mark the click
    canvas.create_oval(x - 3, y - 3, x + 3, y + 3, fill='red', outline='red')
    
    if len(clicks) == 1:
        label.config(text="Clicked Top-Left. Now click Top-Right.")
    elif len(clicks) == 2:
        label.config(text="Clicked Top-Right. Now click Bottom-Left.")
    elif len(clicks) == 3:
        label.config(text="Clicked Bottom-Left. Now click Bottom-Right.")
    elif len(clicks) == 4:
        label.config(text="All four corners clicked! Saving coordinates...")
        save_coordinates()
        root.after(1500, root.destroy) # Close window after 1.5 seconds

def save_coordinates():
    """Saves the captured coordinates to a JSON file."""
    if len(clicks) == 4:
        coordinates = {
            'top_left': clicks[0],
            'top_right': clicks[1],
            'bottom_left': clicks[2],
            'bottom_right': clicks[3]
        }
        with open(OUTPUT_FILE, 'w') as f:
            json.dump(coordinates, f, indent=4)
        print(f"Coordinates saved to {OUTPUT_FILE}")
    else:
        print("Error: Not enough coordinates captured.")

# --- Main Application Setup ---
root = tk.Tk()
root.title("Opponent Grid Calibrator")

# Load the image
image = Image.open(IMAGE_PATH)
photo = ImageTk.PhotoImage(image)

# Create a canvas to display the image
canvas = tk.Canvas(root, width=image.width, height=image.height)
canvas.pack()
canvas.create_image(0, 0, anchor=tk.NW, image=photo)
canvas.bind("<Button-1>", on_image_click)

# Add an instruction label
label = tk.Label(root, text="Click the Top-Left corner of the opponent's inventory grid.", font=("Arial", 12), pady=10)
label.pack()

root.mainloop()
