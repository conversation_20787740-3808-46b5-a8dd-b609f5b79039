import json
from bbagent.game_manager import <PERSON><PERSON><PERSON><PERSON>
from bbagent.optimizer import InventoryOptimizer
from bbagent.action_executor import ActionExecutor

if __name__ == "__main__":
    """
    The main entry point for the Backpack Battles Agent.
    """
    print("Starting Backpack Battles Agent...")
    game_manager = GameManager(player_class="Ranger")

    # Instantiate and run the optimizer
    optimizer = InventoryOptimizer(game_manager)
    best_layout = optimizer.optimize_inventory(generations=5, population_size=10)

    if best_layout:
        # Generate the action plan
        action_executor = ActionExecutor(game_manager)
        actions = action_executor.generate_action_plan(best_layout)

        # Prepare the final JSON output
        output = {
            "reasoning": "The system analyzed the current inventory, ran an optimization algorithm to find the best layout, and is now presenting the required actions to achieve this layout.",
            "actions": actions
        }
        
        # Print the JSON output
        print(json.dumps(output, indent=4))
        
    else:
        print(json.dumps({"reasoning": "Could not determine an optimal layout.", "actions": []}, indent=4))

    # The agent's run loop is commented out as the primary goal is the action plan.
    # game_manager.run()