"""
Enhanced Shop System for Backpack Battles Simulation

Implements the sophisticated shop mechanics from DeepResearch.txt Section 2:
- Rarity-based weighted sampling with round progression
- Pity timers for sales and bags
- Class and subclass filtering
- Unique item system
- Proper reroll mechanics
"""

import random
import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from simulator.core import Item, Player


class ItemRarity(Enum):
    """Item rarity levels"""
    COMMON = "Common"
    RARE = "Rare"
    EPIC = "Epic"
    LEGENDARY = "Legendary"
    GODLY = "Godly"
    UNIQUE = "Unique"


@dataclass
class ShopState:
    """Current state of the shop"""
    offerings: List[Optional[Item]]
    costs: List[int]  # Actual costs (may be on sale)
    original_costs: List[int]  # Original costs before sales
    locks: List[bool]  # Reserved items
    reroll_cost: int
    rerolls_this_round: int
    
    def __post_init__(self):
        if len(self.offerings) != 5:
            self.offerings = [None] * 5
        if len(self.costs) != 5:
            self.costs = [0] * 5
        if len(self.original_costs) != 5:
            self.original_costs = [0] * 5
        if len(self.locks) != 5:
            self.locks = [False] * 5


class PityTimerManager:
    """Manages pity timers for shop mechanics"""
    
    def __init__(self):
        self.items_since_last_sale = 0
        self.items_since_last_bag = 0
        self.sale_pity_threshold = 16  # Guarantee sale after 16 items
        self.bag_pity_threshold = 10   # Guarantee bag after 10 items
        
    def increment_counters(self, items_generated: int = 5) -> None:
        """Increment counters when items are generated"""
        self.items_since_last_sale += items_generated
        self.items_since_last_bag += items_generated
    
    def reset_sale_counter(self) -> None:
        """Reset sale counter when a sale occurs"""
        self.items_since_last_sale = 0
    
    def reset_bag_counter(self) -> None:
        """Reset bag counter when a bag appears"""
        self.items_since_last_bag = 0
    
    def should_force_sale(self) -> bool:
        """Check if a sale should be forced due to pity timer"""
        return self.items_since_last_sale >= self.sale_pity_threshold
    
    def should_force_bag(self) -> bool:
        """Check if a bag should be forced due to pity timer"""
        return self.items_since_last_bag >= self.bag_pity_threshold


class EnhancedShop:
    """Enhanced shop system with all research-based mechanics"""
    
    # Rarity probabilities by round (Table 2.1 from research)
    RARITY_PROBABILITIES = {
        1: {"Common": 0.90, "Rare": 0.10, "Epic": 0.00, "Legendary": 0.00, "Godly": 0.00},
        2: {"Common": 0.84, "Rare": 0.15, "Epic": 0.01, "Legendary": 0.00, "Godly": 0.00},
        3: {"Common": 0.75, "Rare": 0.20, "Epic": 0.05, "Legendary": 0.00, "Godly": 0.00},
        4: {"Common": 0.64, "Rare": 0.25, "Epic": 0.10, "Legendary": 0.01, "Godly": 0.00},
        5: {"Common": 0.45, "Rare": 0.35, "Epic": 0.15, "Legendary": 0.05, "Godly": 0.00},
        6: {"Common": 0.29, "Rare": 0.40, "Epic": 0.20, "Legendary": 0.10, "Godly": 0.01},
        7: {"Common": 0.20, "Rare": 0.35, "Epic": 0.25, "Legendary": 0.15, "Godly": 0.05},
        8: {"Common": 0.20, "Rare": 0.30, "Epic": 0.25, "Legendary": 0.15, "Godly": 0.10},
        9: {"Common": 0.20, "Rare": 0.28, "Epic": 0.25, "Legendary": 0.15, "Godly": 0.12},
        10: {"Common": 0.20, "Rare": 0.25, "Epic": 0.25, "Legendary": 0.15, "Godly": 0.15},
        11: {"Common": 0.20, "Rare": 0.23, "Epic": 0.23, "Legendary": 0.17, "Godly": 0.17},
    }
    
    # Rounds 12-18 all have equal probabilities
    LATE_GAME_PROBABILITIES = {"Common": 0.20, "Rare": 0.20, "Epic": 0.20, "Legendary": 0.20, "Godly": 0.20}
    
    def __init__(self, all_items: Dict[str, Item], use_shop_blacklist: bool = False):
        """
        Initialize the enhanced shop
        
        Args:
            all_items: Dictionary of all items in the game
            use_shop_blacklist: Whether to use the historical blacklist system (should be False)
        """
        self.all_items = all_items
        self.use_shop_blacklist = use_shop_blacklist
        
        # Categorize items by rarity and class
        self.items_by_rarity = self._categorize_items_by_rarity()
        self.items_by_class = self._categorize_items_by_class()
        self.bag_items = self._identify_bag_items()
        
        # Shop state
        self.state = ShopState([], [], [], [], 1, 0)
        self.pity_timers = PityTimerManager()
        
        # Blacklist system (if enabled)
        self.blacklisted_items: List[str] = []
        self.blacklist_counters: Dict[str, int] = {}
        
        # Unique item tracking
        self.unique_item_owned = False
        self.unique_chance_base = 0.02  # 2% base chance from Round 4
        
    def _categorize_items_by_rarity(self) -> Dict[str, List[Item]]:
        """Categorize all items by rarity"""
        categories = {rarity.value: [] for rarity in ItemRarity}
        
        for item in self.all_items.values():
            rarity = getattr(item, 'rarity', 'Common')
            if rarity in categories:
                categories[rarity].append(item)
        
        return categories
    
    def _categorize_items_by_class(self) -> Dict[str, List[Item]]:
        """Categorize items by class restriction"""
        categories = {
            "Neutral": [],
            "Berserker": [],
            "Pyromancer": [],
            "Reaper": [],
            "Ranger": []
        }
        
        for item in self.all_items.values():
            item_class = getattr(item, 'item_class', 'Neutral')
            if item_class in categories:
                categories[item_class].append(item)
        
        return categories
    
    def _identify_bag_items(self) -> List[Item]:
        """Identify all bag items for pity timer"""
        bags = []
        for item in self.all_items.values():
            item_type = getattr(item, 'type', '')
            if item_type.lower() == 'bag':
                bags.append(item)
        return bags
    
    def generate_shop_offerings(self, current_round: int, player_class: str, 
                              subclass: Optional[str] = None, 
                              force_new_round: bool = False) -> ShopState:
        """
        Generate new shop offerings
        
        Args:
            current_round: Current game round (1-18)
            player_class: Player's class
            subclass: Player's subclass (if any)
            force_new_round: Whether this is a new round (affects unique item chance)
        """
        # Don't replace locked items unless it's a new round
        if not force_new_round:
            locked_indices = [i for i, locked in enumerate(self.state.locks) if locked]
        else:
            locked_indices = []
            self.state.rerolls_this_round = 0
        
        # Generate items for non-locked slots
        for i in range(5):
            if i not in locked_indices:
                self.state.offerings[i] = self._generate_single_item(
                    current_round, player_class, subclass, force_new_round and i == 0
                )
                
                # Set costs
                if self.state.offerings[i]:
                    base_cost = self.state.offerings[i].cost
                    is_on_sale = self._check_for_sale(i)
                    
                    if is_on_sale:
                        sale_cost = max(1, math.ceil(base_cost / 2))
                        self.state.costs[i] = sale_cost
                        self.state.original_costs[i] = base_cost
                        self.pity_timers.reset_sale_counter()
                    else:
                        self.state.costs[i] = base_cost
                        self.state.original_costs[i] = base_cost
        
        # Update pity timers
        items_generated = 5 - len(locked_indices)
        self.pity_timers.increment_counters(items_generated)
        
        # Update reroll cost
        if not force_new_round:
            self.state.rerolls_this_round += 1
            if self.state.rerolls_this_round <= 4:
                self.state.reroll_cost = 1
            else:
                self.state.reroll_cost = 2
        else:
            self.state.reroll_cost = 1
        
        return self.state
    
    def _generate_single_item(self, current_round: int, player_class: str, 
                            subclass: Optional[str], check_unique: bool = False) -> Optional[Item]:
        """Generate a single item"""
        
        # Check for unique item (only on new rounds from Round 4)
        if check_unique and current_round >= 4 and not self.unique_item_owned:
            if random.random() < self.unique_chance_base:
                unique_items = self.items_by_rarity.get("Unique", [])
                if unique_items:
                    return random.choice(unique_items)
        
        # Check pity timers
        if self.pity_timers.should_force_bag():
            available_bags = self._filter_items_by_class(self.bag_items, player_class, subclass)
            if available_bags:
                self.pity_timers.reset_bag_counter()
                return random.choice(available_bags)
        
        # Normal item generation
        rarity = self._select_rarity(current_round)
        available_items = self._get_available_items(rarity, player_class, subclass)
        
        if not available_items:
            return None
        
        # Apply blacklist if enabled
        if self.use_shop_blacklist:
            available_items = [item for item in available_items 
                             if item.name not in self.blacklisted_items]
        
        if not available_items:
            return None
        
        selected_item = random.choice(available_items)
        
        # Check if it's a bag for pity timer
        if getattr(selected_item, 'type', '').lower() == 'bag':
            self.pity_timers.reset_bag_counter()
        
        return selected_item
    
    def _select_rarity(self, current_round: int) -> str:
        """Select item rarity based on round probabilities"""
        if current_round <= 11:
            probabilities = self.RARITY_PROBABILITIES[current_round]
        else:
            probabilities = self.LATE_GAME_PROBABILITIES
        
        rand = random.random()
        cumulative = 0.0
        
        for rarity, prob in probabilities.items():
            cumulative += prob
            if rand <= cumulative:
                return rarity
        
        return "Common"  # Fallback
    
    def _get_available_items(self, rarity: str, player_class: str, 
                           subclass: Optional[str]) -> List[Item]:
        """Get available items of specified rarity for the player"""
        rarity_items = self.items_by_rarity.get(rarity, [])
        return self._filter_items_by_class(rarity_items, player_class, subclass)
    
    def _filter_items_by_class(self, items: List[Item], player_class: str, 
                             subclass: Optional[str]) -> List[Item]:
        """Filter items by class and subclass restrictions"""
        available = []
        
        for item in items:
            item_class = getattr(item, 'item_class', 'Neutral')
            
            # Check class restriction
            if item_class == 'Neutral' or item_class == player_class:
                # Check subclass gating
                required_subclass = getattr(item, 'required_subclass', None)
                if required_subclass is None or required_subclass == subclass:
                    available.append(item)
        
        return available
    
    def _check_for_sale(self, slot_index: int) -> bool:
        """Check if an item should be on sale"""
        # Force sale due to pity timer
        if self.pity_timers.should_force_sale():
            return True
        
        # Normal 10% chance
        return random.random() < 0.10
    
    def buy_item(self, player: Player, slot_index: int) -> bool:
        """
        Buy an item from the shop
        
        Args:
            player: Player making the purchase
            slot_index: Shop slot index (0-4)
            
        Returns:
            True if purchase successful
        """
        if not (0 <= slot_index < 5):
            return False
        
        item = self.state.offerings[slot_index]
        cost = self.state.costs[slot_index]
        
        if not item or player.gold < cost:
            return False
        
        # Check if it's a unique item
        if getattr(item, 'rarity', '') == 'Unique':
            if self.unique_item_owned:
                return False
            self.unique_item_owned = True
        
        # Process purchase
        player.gold -= cost
        self.state.offerings[slot_index] = None
        self.state.costs[slot_index] = 0
        self.state.original_costs[slot_index] = 0
        
        # Update blacklist if enabled
        if self.use_shop_blacklist:
            self._update_blacklist_on_purchase(item.name)
        
        return True
    
    def reroll_shop(self, player: Player, current_round: int, player_class: str,
                   subclass: Optional[str] = None) -> bool:
        """
        Reroll the shop
        
        Args:
            player: Player requesting reroll
            current_round: Current game round
            player_class: Player's class
            subclass: Player's subclass
            
        Returns:
            True if reroll successful
        """
        if player.gold < self.state.reroll_cost:
            return False
        
        player.gold -= self.state.reroll_cost
        
        # Update blacklist if enabled
        if self.use_shop_blacklist:
            self._update_blacklist_on_reroll()
        
        # Generate new offerings
        self.generate_shop_offerings(current_round, player_class, subclass, False)
        
        return True
    
    def toggle_lock(self, slot_index: int) -> bool:
        """Toggle lock status of a shop slot"""
        if 0 <= slot_index < 5:
            self.state.locks[slot_index] = not self.state.locks[slot_index]
            return True
        return False
    
    def _update_blacklist_on_purchase(self, item_name: str) -> None:
        """Update blacklist when item is purchased"""
        # Buying an item prevents it from appearing on next reroll
        if item_name not in self.blacklisted_items:
            self.blacklisted_items.append(item_name)
            self.blacklist_counters[item_name] = 1
    
    def _update_blacklist_on_reroll(self) -> None:
        """Update blacklist when shop is rerolled"""
        # Add current items to blacklist (passing prevents 2-3 rerolls)
        for item in self.state.offerings:
            if item and item.name not in self.blacklisted_items:
                self.blacklisted_items.append(item.name)
                self.blacklist_counters[item.name] = random.randint(2, 3)
        
        # Decrement counters and remove expired items
        expired = []
        for item_name, count in self.blacklist_counters.items():
            self.blacklist_counters[item_name] = count - 1
            if self.blacklist_counters[item_name] <= 0:
                expired.append(item_name)
        
        for item_name in expired:
            if item_name in self.blacklisted_items:
                self.blacklisted_items.remove(item_name)
            del self.blacklist_counters[item_name]
    
    def get_shop_state(self) -> Dict[str, Any]:
        """Get current shop state for serialization"""
        return {
            "offerings": [item.name if item else None for item in self.state.offerings],
            "costs": self.state.costs.copy(),
            "original_costs": self.state.original_costs.copy(),
            "locks": self.state.locks.copy(),
            "reroll_cost": self.state.reroll_cost,
            "rerolls_this_round": self.state.rerolls_this_round,
            "pity_timers": {
                "items_since_last_sale": self.pity_timers.items_since_last_sale,
                "items_since_last_bag": self.pity_timers.items_since_last_bag
            },
            "unique_item_owned": self.unique_item_owned
        }
