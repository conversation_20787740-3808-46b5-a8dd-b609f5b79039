import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageDraw
import json
import os
import random
import math

class InteractiveInventoryTrainer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Interactive Inventory Trainer")
        self.root.geometry("1600x1000")
        
        # Data
        self.empty_inventory = None
        self.current_inventory = None
        self.grid_calibration = None
        self.item_templates = {}
        self.placed_items = []
        self.current_items_to_place = []
        self.dragging_item = None
        self.dragging_placed_item = None
        self.drag_offset_x = 0
        self.drag_offset_y = 0
        self.training_data = []
        
        # Grid settings
        self.grid_rows = 7
        self.grid_cols = 9
        self.grid_cells = []
        
        # Setup UI first
        self.setup_ui()

        # Then load data
        self.load_empty_inventory()
        self.load_item_templates()
        
    def setup_ui(self):
        """Setup the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control panel (left side)
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.configure(width=350)
        
        # Grid calibration section
        calib_frame = ttk.LabelFrame(control_frame, text="1. Grid Calibration", padding=10)
        calib_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(calib_frame, text="Click corners of inventory grid:").pack(anchor=tk.W)
        self.calib_status = ttk.Label(calib_frame, text="Status: Not calibrated", foreground='red')
        self.calib_status.pack(anchor=tk.W)
        
        ttk.Button(calib_frame, text="🎯 Start Grid Calibration", 
                  command=self.start_grid_calibration).pack(fill=tk.X, pady=(5, 0))
        
        # Item placement section
        items_frame = ttk.LabelFrame(control_frame, text="2. Item Placement", padding=10)
        items_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(items_frame, text="🎲 Load 5 Random Items", 
                  command=self.load_random_items).pack(fill=tk.X, pady=(0, 5))
        
        # Current items list
        ttk.Label(items_frame, text="Items to place:").pack(anchor=tk.W, pady=(10, 0))
        self.items_listbox = tk.Listbox(items_frame, height=6)
        self.items_listbox.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(items_frame, text="Drag items from list to inventory!", 
                 font=('Arial', 9, 'italic')).pack(anchor=tk.W)
        
        # Action buttons
        action_frame = ttk.LabelFrame(control_frame, text="3. Actions", padding=10)
        action_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(action_frame, text="📊 Record Placement Data", 
                  command=self.record_placement_data, 
                  style='Accent.TButton').pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(action_frame, text="🗑️ Clear Inventory", 
                  command=self.clear_inventory).pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(action_frame, text="💾 Save Training Data", 
                  command=self.save_training_data).pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(action_frame, text="🔄 Reset All", 
                  command=self.reset_all).pack(fill=tk.X)
        
        # Progress section
        progress_frame = ttk.LabelFrame(control_frame, text="Progress", padding=10)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.progress_label = ttk.Label(progress_frame, text="Training samples: 0")
        self.progress_label.pack(anchor=tk.W)
        
        self.placed_label = ttk.Label(progress_frame, text="Items placed: 0")
        self.placed_label.pack(anchor=tk.W)
        
        # Instructions
        inst_frame = ttk.LabelFrame(control_frame, text="Instructions", padding=10)
        inst_frame.pack(fill=tk.X)
        
        instructions = """
1. Calibrate grid by clicking 4 corners
2. Load random items
3. Drag items to inventory positions
4. Record data when satisfied
5. Clear and repeat for more samples
        """
        ttk.Label(inst_frame, text=instructions, justify=tk.LEFT, 
                 font=('Arial', 8)).pack()
        
        # Canvas (right side)
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.canvas = tk.Canvas(canvas_frame, bg='white', cursor='crosshair')
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Bind events
        self.canvas.bind('<Button-1>', self.on_canvas_click)
        self.canvas.bind('<B1-Motion>', self.on_canvas_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_canvas_release)
        self.items_listbox.bind('<Button-1>', self.on_item_select)
        self.items_listbox.bind('<B1-Motion>', self.on_item_drag)
        
    def load_empty_inventory(self):
        """Load the empty inventory image."""
        img_path = "data/screenshots/Empty inventory storage and shop.png"
        if os.path.exists(img_path):
            self.empty_inventory = Image.open(img_path)
            print(f"Loaded empty inventory: {self.empty_inventory.size}")
            self.current_inventory = self.empty_inventory.copy()
            self.display_inventory()
        else:
            messagebox.showerror("Error", f"Could not find '{img_path}'")
    
    def load_item_templates(self):
        """Load item templates at 0.5 scale."""
        item_dir = "data/item_images"
        if not os.path.exists(item_dir):
            messagebox.showerror("Error", f"Item directory '{item_dir}' not found")
            return
            
        for filename in os.listdir(item_dir):
            if filename.endswith('.png'):
                item_name = filename.replace('.png', '').replace('_', ' ')
                try:
                    img_path = os.path.join(item_dir, filename)
                    img = Image.open(img_path).convert('RGBA')
                    
                    # Scale to 0.5 as discovered
                    scaled_w = int(img.width * 0.5)
                    scaled_h = int(img.height * 0.5)
                    scaled_img = img.resize((scaled_w, scaled_h), Image.LANCZOS)
                    
                    self.item_templates[item_name] = scaled_img
                except Exception as e:
                    print(f"Error loading {filename}: {e}")
        
        print(f"Loaded {len(self.item_templates)} item templates at 0.5 scale")
    
    def display_inventory(self):
        """Display the current inventory state."""
        if not self.current_inventory:
            return
            
        # Create display image
        display_img = self.current_inventory.copy()
        draw = ImageDraw.Draw(display_img)
        
        # Draw grid if calibrated
        if self.grid_calibration:
            self.draw_grid_overlay(draw)
        
        # Draw placed items with drag zones
        for item in self.placed_items:
            bbox = item['bbox']
            draw.rectangle(bbox, outline='green', width=2)
            draw.text((bbox[0], bbox[1]-15), item['name'], fill='green')

            # Draw yellow drag circle in center
            center_x = item['center_x']
            center_y = item['center_y']
            drag_radius = 15

            # Yellow circle for drag zone
            circle_bbox = [
                center_x - drag_radius, center_y - drag_radius,
                center_x + drag_radius, center_y + drag_radius
            ]
            draw.ellipse(circle_bbox, outline='yellow', width=2)
        
        # Convert and display
        self.photo = ImageTk.PhotoImage(display_img)
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def start_grid_calibration(self):
        """Start grid calibration mode."""
        self.grid_calibration = {'corners': [], 'mode': 'calibrating'}
        self.calib_status.config(text="Click top-left corner", foreground='orange')
        messagebox.showinfo("Grid Calibration", 
                          "Click the 4 corners of the inventory grid:\n"
                          "1. Top-left\n2. Top-right\n3. Bottom-left\n4. Bottom-right")
    
    def on_canvas_click(self, event):
        """Handle canvas clicks."""
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)

        # Grid calibration mode
        if self.grid_calibration and self.grid_calibration.get('mode') == 'calibrating':
            self.add_calibration_point(x, y)
            return

        # Check if clicking on a placed item's drag zone
        clicked_item = self.get_item_at_position(x, y)
        if clicked_item:
            self.start_dragging_placed_item(clicked_item, x, y)
            return

        # Item placement mode (placing new item from list)
        if self.dragging_item:
            self.place_item_at(x, y)
    
    def add_calibration_point(self, x, y):
        """Add a calibration point."""
        corners = self.grid_calibration['corners']
        corners.append((int(x), int(y)))
        
        status_messages = [
            "Click top-right corner",
            "Click bottom-left corner", 
            "Click bottom-right corner",
            "Grid calibrated!"
        ]
        
        if len(corners) < 4:
            self.calib_status.config(text=status_messages[len(corners)-1], foreground='orange')
        else:
            # Calibration complete
            self.complete_grid_calibration()
            self.calib_status.config(text="Grid calibrated ✓", foreground='green')
    
    def complete_grid_calibration(self):
        """Complete grid calibration and calculate cell positions."""
        corners = self.grid_calibration['corners']
        
        # Calculate grid cell positions
        tl, tr, bl, br = corners
        
        self.grid_cells = []
        for row in range(self.grid_rows):
            row_cells = []
            for col in range(self.grid_cols):
                # Bilinear interpolation
                row_ratio = row / (self.grid_rows - 1) if self.grid_rows > 1 else 0
                col_ratio = col / (self.grid_cols - 1) if self.grid_cols > 1 else 0
                
                # Interpolate top and bottom edges
                top_x = tl[0] + (tr[0] - tl[0]) * col_ratio
                top_y = tl[1] + (tr[1] - tl[1]) * col_ratio
                bottom_x = bl[0] + (br[0] - bl[0]) * col_ratio
                bottom_y = bl[1] + (br[1] - bl[1]) * col_ratio
                
                # Interpolate between top and bottom
                cell_x = top_x + (bottom_x - top_x) * row_ratio
                cell_y = top_y + (bottom_y - top_y) * row_ratio
                
                # Calculate cell size
                cell_w = abs(tr[0] - tl[0]) / self.grid_cols
                cell_h = abs(bl[1] - tl[1]) / self.grid_rows
                
                row_cells.append({
                    'center_x': int(cell_x),
                    'center_y': int(cell_y),
                    'width': int(cell_w),
                    'height': int(cell_h)
                })
            self.grid_cells.append(row_cells)
        
        self.grid_calibration['mode'] = 'complete'
        self.display_inventory()
    
    def draw_grid_overlay(self, draw):
        """Draw grid overlay on the image."""
        if not self.grid_cells:
            return
            
        for row in range(self.grid_rows):
            for col in range(self.grid_cols):
                cell = self.grid_cells[row][col]
                x1 = cell['center_x'] - cell['width'] // 2
                y1 = cell['center_y'] - cell['height'] // 2
                x2 = cell['center_x'] + cell['width'] // 2
                y2 = cell['center_y'] + cell['height'] // 2
                
                draw.rectangle([x1, y1, x2, y2], outline='blue', width=1)
    
    def load_random_items(self):
        """Load 5 random items to place."""
        if not self.grid_calibration or self.grid_calibration.get('mode') != 'complete':
            messagebox.showwarning("Warning", "Please calibrate the grid first!")
            return
            
        # Select 5 random items
        item_names = list(self.item_templates.keys())
        selected = random.sample(item_names, min(5, len(item_names)))
        
        self.current_items_to_place = selected
        
        # Update listbox
        self.items_listbox.delete(0, tk.END)
        for item in selected:
            self.items_listbox.insert(tk.END, item)
        
        print(f"Loaded items: {selected}")
    
    def on_item_select(self, event):
        """Handle item selection from listbox."""
        selection = self.items_listbox.curselection()
        if selection:
            item_name = self.items_listbox.get(selection[0])
            self.dragging_item = item_name
            print(f"Selected item: {item_name}")
    
    def on_item_drag(self, event):
        """Handle dragging item from listbox."""
        # This could be enhanced to show a preview
        pass
    
    def place_item_at(self, x, y):
        """Place the dragging item at the specified position."""
        if not self.dragging_item or self.dragging_item not in self.item_templates:
            return
            
        item_img = self.item_templates[self.dragging_item]
        
        # Calculate placement
        item_w, item_h = item_img.size
        x1 = int(x - item_w // 2)
        y1 = int(y - item_h // 2)
        x2 = x1 + item_w
        y2 = y1 + item_h
        
        # Find which grid cell this corresponds to
        grid_pos = self.pixel_to_grid(x, y)
        
        # Place item on inventory
        self.current_inventory.paste(item_img, (x1, y1), item_img)
        
        # Record placement
        placement = {
            'name': self.dragging_item,
            'bbox': [x1, y1, x2, y2],
            'center_x': int(x),
            'center_y': int(y),
            'width': item_w,
            'height': item_h,
            'grid_row': grid_pos[1] if grid_pos else -1,
            'grid_col': grid_pos[0] if grid_pos else -1
        }
        
        self.placed_items.append(placement)
        
        # Remove from items to place
        if self.dragging_item in self.current_items_to_place:
            self.current_items_to_place.remove(self.dragging_item)
            
        # Update listbox
        self.items_listbox.delete(0, tk.END)
        for item in self.current_items_to_place:
            self.items_listbox.insert(tk.END, item)
        
        self.dragging_item = None
        self.update_status()
        self.display_inventory()
    
    def pixel_to_grid(self, x, y):
        """Convert pixel coordinates to grid position."""
        if not self.grid_cells:
            return None
            
        min_dist = float('inf')
        best_pos = None
        
        for row in range(self.grid_rows):
            for col in range(self.grid_cols):
                cell = self.grid_cells[row][col]
                dist = math.sqrt((x - cell['center_x'])**2 + (y - cell['center_y'])**2)
                if dist < min_dist:
                    min_dist = dist
                    best_pos = (col, row)
        
        return best_pos
    
    def record_placement_data(self):
        """Record the current placement as training data."""
        if not self.placed_items:
            messagebox.showwarning("Warning", "No items placed to record!")
            return
            
        training_sample = {
            'sample_id': len(self.training_data),
            'grid_calibration': self.grid_calibration['corners'],
            'placed_items': self.placed_items.copy(),
            'grid_size': (self.grid_cols, self.grid_rows)
        }
        
        self.training_data.append(training_sample)
        
        messagebox.showinfo("Recorded", f"Recorded training sample #{len(self.training_data)}")
        self.update_status()
    
    def clear_inventory(self):
        """Clear all placed items."""
        self.current_inventory = self.empty_inventory.copy()
        self.placed_items = []
        self.display_inventory()
        self.update_status()
    
    def update_status(self):
        """Update status labels."""
        self.progress_label.config(text=f"Training samples: {len(self.training_data)}")
        self.placed_label.config(text=f"Items placed: {len(self.placed_items)}")
    
    def save_training_data(self):
        """Save all training data."""
        if not self.training_data:
            messagebox.showwarning("Warning", "No training data to save!")
            return
            
        with open('inventory_training_data.json', 'w') as f:
            json.dump(self.training_data, f, indent=2)
            
        messagebox.showinfo("Saved", f"Saved {len(self.training_data)} training samples!")
    
    def reset_all(self):
        """Reset everything."""
        self.grid_calibration = None
        self.placed_items = []
        self.current_items_to_place = []
        self.training_data = []
        self.current_inventory = self.empty_inventory.copy() if self.empty_inventory else None
        self.calib_status.config(text="Status: Not calibrated", foreground='red')
        self.items_listbox.delete(0, tk.END)
        self.update_status()
        self.display_inventory()
    
    def on_canvas_drag(self, event):
        """Handle canvas dragging."""
        if self.dragging_placed_item:
            x = self.canvas.canvasx(event.x)
            y = self.canvas.canvasy(event.y)

            # Update item position
            new_x = x - self.drag_offset_x
            new_y = y - self.drag_offset_y

            self.update_placed_item_position(self.dragging_placed_item, new_x, new_y)

    def on_canvas_release(self, event):
        """Handle canvas release."""
        if self.dragging_placed_item:
            # Finalize the drag
            self.dragging_placed_item = None
            self.drag_offset_x = 0
            self.drag_offset_y = 0
            self.rebuild_inventory_image()
            self.display_inventory()
    
    def get_item_at_position(self, x, y):
        """Check if position is within a placed item's drag zone."""
        for item in self.placed_items:
            center_x = item['center_x']
            center_y = item['center_y']
            drag_radius = 15

            # Check if click is within drag circle
            distance = math.sqrt((x - center_x)**2 + (y - center_y)**2)
            if distance <= drag_radius:
                return item
        return None

    def start_dragging_placed_item(self, item, x, y):
        """Start dragging a placed item."""
        self.dragging_placed_item = item
        self.drag_offset_x = x - item['center_x']
        self.drag_offset_y = y - item['center_y']
        print(f"Started dragging: {item['name']}")

    def update_placed_item_position(self, item, new_center_x, new_center_y):
        """Update a placed item's position during drag."""
        # Update center position
        item['center_x'] = int(new_center_x)
        item['center_y'] = int(new_center_y)

        # Update bounding box
        half_w = item['width'] // 2
        half_h = item['height'] // 2
        item['bbox'] = [
            int(new_center_x - half_w),
            int(new_center_y - half_h),
            int(new_center_x + half_w),
            int(new_center_y + half_h)
        ]

        # Update grid position
        grid_pos = self.pixel_to_grid(new_center_x, new_center_y)
        if grid_pos:
            item['grid_col'] = grid_pos[0]
            item['grid_row'] = grid_pos[1]

        # Update display (but don't rebuild inventory image yet for smooth dragging)
        self.display_inventory()

    def rebuild_inventory_image(self):
        """Rebuild the inventory image with all placed items."""
        # Start with empty inventory
        self.current_inventory = self.empty_inventory.copy()

        # Place all items on the inventory
        for item in self.placed_items:
            if item['name'] in self.item_templates:
                item_img = self.item_templates[item['name']]
                bbox = item['bbox']
                self.current_inventory.paste(item_img, (bbox[0], bbox[1]), item_img)

    def run(self):
        """Run the application."""
        self.root.mainloop()

if __name__ == "__main__":
    app = InteractiveInventoryTrainer()
    app.run()
