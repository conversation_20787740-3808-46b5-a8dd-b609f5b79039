["tests/test_core_systems.py::TestCombatEvents::test_event_ordering", "tests/test_core_systems.py::TestCombatEvents::test_start_of_battle_fifo", "tests/test_core_systems.py::TestCombatEvents::test_status_tick_scheduling", "tests/test_core_systems.py::TestDamageSystem::test_accuracy_with_luck_and_blind", "tests/test_core_systems.py::TestDamageSystem::test_base_damage_with_empower", "tests/test_core_systems.py::TestDamageSystem::test_block_application", "tests/test_core_systems.py::TestDamageSystem::test_critical_hit_calculation", "tests/test_core_systems.py::TestDamageSystem::test_vampirism_on_hit", "tests/test_core_systems.py::TestEnhancedShop::test_pity_timer_bag", "tests/test_core_systems.py::TestEnhancedShop::test_pity_timer_sale", "tests/test_core_systems.py::TestEnhancedShop::test_rarity_probabilities", "tests/test_core_systems.py::TestEnhancedShop::test_reroll_cost_progression", "tests/test_core_systems.py::TestEnhancedShop::test_unique_item_restriction", "tests/test_core_systems.py::TestResourceManagement::test_mana_discrete_resource", "tests/test_core_systems.py::TestResourceManagement::test_out_of_stamina_state", "tests/test_core_systems.py::TestResourceManagement::test_stamina_cost_calculations", "tests/test_core_systems.py::TestResourceManagement::test_stamina_regeneration", "tests/test_core_systems.py::TestStatusEffects::test_empower_stacking", "tests/test_core_systems.py::TestStatusEffects::test_heat_cooldown_modifier", "tests/test_core_systems.py::TestStatusEffects::test_luck_accuracy_modifier", "tests/test_core_systems.py::TestStatusEffects::test_poison_tick_damage", "tests/test_core_systems.py::TestStatusEffects::test_status_expiry", "tests/test_environment.py::test_environment_initialization", "tests/test_environment.py::test_reset_method", "tests/test_environment.py::test_step_method"]