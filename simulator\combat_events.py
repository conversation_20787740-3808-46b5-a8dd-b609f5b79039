"""
Combat Event System for Backpack Battles Simulation

Implements the priority queue-based continuous-time event simulation as defined in 
DeepResearch.txt Section 3.2. This replaces the turn-based combat with proper
continuous-time mechanics.
"""

import heapq
import random
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import time


class EventType(Enum):
    """Types of combat events"""
    ITEM_ACTIVATION = "item_activation"
    STATUS_TICK = "status_tick"
    STATUS_EXPIRE = "status_expire"
    RESOURCE_REGEN = "resource_regen"
    FATIGUE_DAMAGE = "fatigue_damage"
    START_OF_BATTLE = "start_of_battle"


@dataclass
class CombatEvent:
    """Represents a single event in the combat simulation"""
    time: float
    priority: int  # Lower = higher priority (for tie-breaking)
    event_type: EventType
    source_id: str  # Item ID, effect name, etc.
    target_player: int  # 0 or 1
    data: Dict[str, Any] = field(default_factory=dict)
    callback: Optional[Callable] = None
    
    def __lt__(self, other):
        """Comparison for heapq (priority queue)"""
        if self.time != other.time:
            return self.time < other.time
        return self.priority < other.priority


class CombatEventQueue:
    """Manages the priority queue of combat events"""
    
    def __init__(self):
        self.queue: List[CombatEvent] = []
        self.current_time: float = 0.0
        self.event_counter: int = 0  # For unique priorities
        
    def add_event(self, event: CombatEvent) -> None:
        """Add an event to the queue"""
        # Ensure unique priority for insertion order
        if event.priority == 0:
            event.priority = self.event_counter
            self.event_counter += 1
            
        heapq.heappush(self.queue, event)
    
    def get_next_event(self) -> Optional[CombatEvent]:
        """Get the next event from the queue"""
        if self.queue:
            return heapq.heappop(self.queue)
        return None
    
    def peek_next_time(self) -> Optional[float]:
        """Get the time of the next event without removing it"""
        if self.queue:
            return self.queue[0].time
        return None
    
    def has_events(self) -> bool:
        """Check if there are events in the queue"""
        return len(self.queue) > 0
    
    def clear(self) -> None:
        """Clear all events from the queue"""
        self.queue.clear()
        self.current_time = 0.0
        self.event_counter = 0
    
    def remove_events_by_source(self, source_id: str) -> None:
        """Remove all events from a specific source (e.g., when item is destroyed)"""
        self.queue = [event for event in self.queue if event.source_id != source_id]
        heapq.heapify(self.queue)


class CombatEventManager:
    """Manages combat events and their scheduling"""
    
    def __init__(self):
        self.queue = CombatEventQueue()
        self.item_placement_order: Dict[str, int] = {}  # For FIFO start-of-battle effects
        self.placement_counter = 0
        
    def register_item_placement(self, item_id: str) -> None:
        """Register when an item was placed (for start-of-battle ordering)"""
        self.item_placement_order[item_id] = self.placement_counter
        self.placement_counter += 1
    
    def schedule_start_of_battle_effects(self, items_with_effects: List[Dict[str, Any]]) -> None:
        """Schedule start-of-battle effects in FIFO order"""
        # Sort by placement order (FIFO)
        sorted_items = sorted(items_with_effects, 
                            key=lambda x: self.item_placement_order.get(x['item_id'], 999999))
        
        for i, item_info in enumerate(sorted_items):
            event = CombatEvent(
                time=0.0,
                priority=i,  # Maintain FIFO order
                event_type=EventType.START_OF_BATTLE,
                source_id=item_info['item_id'],
                target_player=item_info['player'],
                data=item_info.get('effect_data', {}),
                callback=item_info.get('callback')
            )
            self.queue.add_event(event)
    
    def schedule_item_activation(self, item_id: str, player: int, activation_time: float,
                               effect_data: Dict[str, Any], callback: Callable = None) -> None:
        """Schedule an item activation"""
        # Add small random variance to break symmetry (±10%)
        variance = random.uniform(-0.1, 0.1)
        actual_time = activation_time * (1.0 + variance)
        
        event = CombatEvent(
            time=self.queue.current_time + actual_time,
            priority=0,  # Will be set by add_event
            event_type=EventType.ITEM_ACTIVATION,
            source_id=item_id,
            target_player=player,
            data=effect_data,
            callback=callback
        )
        self.queue.add_event(event)
    
    def schedule_status_tick(self, effect_name: str, player: int, tick_interval: float = 2.0) -> None:
        """Schedule a status effect tick (e.g., poison, regeneration)"""
        event = CombatEvent(
            time=self.queue.current_time + tick_interval,
            priority=1000,  # Lower priority than item activations
            event_type=EventType.STATUS_TICK,
            source_id=effect_name,
            target_player=player,
            data={"effect_name": effect_name}
        )
        self.queue.add_event(event)
    
    def schedule_status_expiry(self, effect_name: str, player: int, duration: float) -> None:
        """Schedule a status effect expiry"""
        event = CombatEvent(
            time=self.queue.current_time + duration,
            priority=500,  # Medium priority
            event_type=EventType.STATUS_EXPIRE,
            source_id=effect_name,
            target_player=player,
            data={"effect_name": effect_name}
        )
        self.queue.add_event(event)
    
    def schedule_fatigue_damage(self, fatigue_start_time: float = 60.0) -> None:
        """Schedule fatigue damage to prevent infinite battles"""
        event = CombatEvent(
            time=fatigue_start_time,
            priority=2000,  # Low priority
            event_type=EventType.FATIGUE_DAMAGE,
            source_id="fatigue",
            target_player=-1,  # Affects both players
            data={"damage": 1}  # Starting damage, will increase over time
        )
        self.queue.add_event(event)
    
    def reschedule_item_activation(self, item_id: str, new_cooldown: float,
                                 effect_data: Dict[str, Any], callback: Callable = None) -> None:
        """Reschedule an item for its next activation"""
        # Remove any existing events for this item
        self.queue.remove_events_by_source(item_id)
        
        # Schedule new activation
        player = effect_data.get('player', 0)
        self.schedule_item_activation(item_id, player, new_cooldown, effect_data, callback)
    
    def process_next_event(self) -> Optional[CombatEvent]:
        """Process the next event in the queue"""
        event = self.queue.get_next_event()
        if event:
            self.queue.current_time = event.time
            return event
        return None
    
    def get_current_time(self) -> float:
        """Get the current simulation time"""
        return self.queue.current_time
    
    def has_events(self) -> bool:
        """Check if there are more events to process"""
        return self.queue.has_events()
    
    def clear(self) -> None:
        """Clear all events and reset"""
        self.queue.clear()
        self.item_placement_order.clear()
        self.placement_counter = 0


class CombatSimulationEngine:
    """Main engine for running the event-driven combat simulation"""
    
    def __init__(self):
        self.event_manager = CombatEventManager()
        self.max_battle_time = 120.0  # 2 minutes max battle time
        self.fatigue_start_time = 60.0  # Fatigue starts after 1 minute
        
    def initialize_battle(self, player1_items: List[Dict], player2_items: List[Dict]) -> None:
        """Initialize a battle with items from both players"""
        self.event_manager.clear()
        
        # Register item placement order for both players
        all_items = []
        
        for item in player1_items:
            item['player'] = 0
            self.event_manager.register_item_placement(item['item_id'])
            all_items.append(item)
            
        for item in player2_items:
            item['player'] = 1
            self.event_manager.register_item_placement(item['item_id'])
            all_items.append(item)
        
        # Schedule start-of-battle effects
        start_effects = [item for item in all_items if item.get('has_start_effect', False)]
        self.event_manager.schedule_start_of_battle_effects(start_effects)
        
        # Schedule initial item activations
        for item in all_items:
            if item.get('cooldown', 0) > 0:
                self.event_manager.schedule_item_activation(
                    item['item_id'],
                    item['player'],
                    item['cooldown'],
                    item,
                    item.get('activation_callback')
                )
        
        # Schedule fatigue damage
        self.event_manager.schedule_fatigue_damage(self.fatigue_start_time)
    
    def run_simulation(self, battle_callback: Callable[[CombatEvent], bool]) -> Dict[str, Any]:
        """
        Run the combat simulation
        
        Args:
            battle_callback: Function that processes events and returns True if battle should continue
            
        Returns:
            Dictionary with battle results
        """
        start_time = time.time()
        events_processed = 0
        
        while (self.event_manager.has_events() and 
               self.event_manager.get_current_time() < self.max_battle_time):
            
            event = self.event_manager.process_next_event()
            if not event:
                break
                
            events_processed += 1
            
            # Process the event through the callback
            should_continue = battle_callback(event)
            if not should_continue:
                break
        
        simulation_time = time.time() - start_time
        
        return {
            "battle_time": self.event_manager.get_current_time(),
            "events_processed": events_processed,
            "simulation_time": simulation_time,
            "ended_by_fatigue": self.event_manager.get_current_time() >= self.fatigue_start_time
        }
