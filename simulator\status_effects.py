"""
Status Effects System for Backpack Battles Simulation

Implements the comprehensive status effect system as defined in DeepResearch.txt Table 3.1.
This system handles buffs, debuffs, stacking logic, and timing mechanics.
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import time


class EffectType(Enum):
    """Type of status effect"""
    BUFF = "buff"
    DEBUFF = "debuff"


class StackingType(Enum):
    """How the effect stacks"""
    ADDITIVE = "additive"  # Stacks add together (e.g., Empower)
    DURATION = "duration"  # Only duration matters (e.g., Stun)
    REPLACE = "replace"    # New effect replaces old one


@dataclass
class StatusEffect:
    """Represents a single status effect instance"""
    name: str
    effect_type: EffectType
    stacking_type: StackingType
    stacks: int = 1
    duration: float = 0.0  # 0 = permanent until removed
    applied_time: float = 0.0
    source_item_id: Optional[str] = None
    
    def is_expired(self, current_time: float) -> bool:
        """Check if this effect has expired"""
        if self.duration <= 0:
            return False  # Permanent effect
        return current_time >= self.applied_time + self.duration


class StatusEffectManager:
    """Manages all status effects for a single player/entity"""
    
    def __init__(self):
        self.effects: Dict[str, StatusEffect] = {}
        self.tick_timer = 0.0  # For DoT/HoT effects that tick every 2 seconds
        
    def add_effect(self, effect_name: str, stacks: int = 1, duration: float = 0.0, 
                   current_time: float = 0.0, source_item_id: Optional[str] = None) -> None:
        """Add or update a status effect"""
        effect_config = STATUS_EFFECT_CONFIGS.get(effect_name)
        if not effect_config:
            print(f"Warning: Unknown status effect '{effect_name}'")
            return
            
        if effect_name in self.effects:
            existing = self.effects[effect_name]
            
            if effect_config["stacking_type"] == StackingType.ADDITIVE:
                existing.stacks += stacks
            elif effect_config["stacking_type"] == StackingType.DURATION:
                # Refresh duration
                existing.duration = duration
                existing.applied_time = current_time
            elif effect_config["stacking_type"] == StackingType.REPLACE:
                existing.stacks = stacks
                existing.duration = duration
                existing.applied_time = current_time
        else:
            self.effects[effect_name] = StatusEffect(
                name=effect_name,
                effect_type=effect_config["type"],
                stacking_type=effect_config["stacking_type"],
                stacks=stacks,
                duration=duration,
                applied_time=current_time,
                source_item_id=source_item_id
            )
    
    def remove_effect(self, effect_name: str) -> None:
        """Remove a status effect completely"""
        if effect_name in self.effects:
            del self.effects[effect_name]
    
    def reduce_stacks(self, effect_name: str, amount: int = 1) -> None:
        """Reduce stacks of an effect, removing it if stacks reach 0"""
        if effect_name in self.effects:
            self.effects[effect_name].stacks -= amount
            if self.effects[effect_name].stacks <= 0:
                self.remove_effect(effect_name)
    
    def get_effect_stacks(self, effect_name: str) -> int:
        """Get the number of stacks of an effect"""
        if effect_name in self.effects:
            return self.effects[effect_name].stacks
        return 0
    
    def has_effect(self, effect_name: str) -> bool:
        """Check if an effect is active"""
        return effect_name in self.effects
    
    def update(self, current_time: float, delta_time: float) -> List[str]:
        """Update all effects, return list of effects that triggered"""
        triggered_effects = []
        
        # Remove expired effects
        expired = [name for name, effect in self.effects.items() 
                  if effect.is_expired(current_time)]
        for name in expired:
            self.remove_effect(name)
        
        # Handle tick-based effects (every 2 seconds)
        self.tick_timer += delta_time
        if self.tick_timer >= 2.0:
            self.tick_timer = 0.0
            
            # Process DoT/HoT effects
            for effect_name in ["Poison", "Regeneration"]:
                if self.has_effect(effect_name):
                    triggered_effects.append(effect_name)
        
        return triggered_effects
    
    def get_stat_modifier(self, stat_name: str) -> float:
        """Get the total modifier for a stat from all active effects"""
        modifier = 0.0
        
        # Damage modifiers
        if stat_name == "damage":
            modifier += self.get_effect_stacks("Empower")
        
        # Accuracy modifiers
        elif stat_name == "accuracy_multiplier":
            luck_stacks = self.get_effect_stacks("Luck")
            blind_stacks = self.get_effect_stacks("Blind")

            # Luck increases accuracy: FinalAccuracy = BaseAccuracy * (1 + 0.05 * Luck)
            luck_multiplier = (1.0 + 0.05 * luck_stacks) if luck_stacks > 0 else 1.0

            # Blind decreases accuracy: FinalAccuracy = BaseAccuracy * (1 - 0.05 * Blind)
            blind_multiplier = (1.0 - 0.05 * blind_stacks) if blind_stacks > 0 else 1.0

            modifier = luck_multiplier * blind_multiplier
            return modifier

        # Cooldown modifiers
        elif stat_name == "cooldown_multiplier":
            heat_stacks = self.get_effect_stacks("Heat")
            cold_stacks = self.get_effect_stacks("Cold")

            # Heat makes cooldowns faster: EffectiveCooldown = BaseCooldown / (1 + 0.02 * Heat_Stacks)
            heat_multiplier = 1.0 / (1.0 + 0.02 * heat_stacks) if heat_stacks > 0 else 1.0

            # Cold makes cooldowns slower: EffectiveCooldown = BaseCooldown * (1 + 0.02 * Cold_Stacks)
            cold_multiplier = (1.0 + 0.02 * cold_stacks) if cold_stacks > 0 else 1.0

            modifier = heat_multiplier * cold_multiplier
            return modifier
            
        return modifier
    
    def apply_damage_effect(self, effect_name: str, base_damage: int) -> int:
        """Apply damage-based effects like Spikes reflection"""
        if effect_name == "Spikes" and self.has_effect("Spikes"):
            return min(base_damage, self.get_effect_stacks("Spikes"))
        return 0
    
    def apply_healing_effect(self, effect_name: str, base_healing: int) -> int:
        """Apply healing-based effects like Vampirism"""
        if effect_name == "Vampirism" and self.has_effect("Vampirism"):
            return min(base_healing, self.get_effect_stacks("Vampirism"))
        return 0


# Status Effect Configuration Database
# Based on Table 3.1 from DeepResearch.txt
STATUS_EFFECT_CONFIGS = {
    # Buffs
    "Empower": {
        "type": EffectType.BUFF,
        "stacking_type": StackingType.ADDITIVE,
        "description": "Increases weapon damage by 1 per stack"
    },
    "Heat": {
        "type": EffectType.BUFF,
        "stacking_type": StackingType.ADDITIVE,
        "description": "All item cooldowns trigger faster. EffectiveCooldown = BaseCooldown / (1 + 0.02 * Heat_Stacks)"
    },
    "Luck": {
        "type": EffectType.BUFF,
        "stacking_type": StackingType.ADDITIVE,
        "description": "Increases accuracy by 5% per stack"
    },
    "Regeneration": {
        "type": EffectType.BUFF,
        "stacking_type": StackingType.ADDITIVE,
        "description": "Heals 1 health per stack every 2 seconds"
    },
    "Spikes": {
        "type": EffectType.BUFF,
        "stacking_type": StackingType.ADDITIVE,
        "description": "When hit by a melee attack, reflect damage equal to min(IncomingDamage, Spikes_Stacks)"
    },
    "Vampirism": {
        "type": EffectType.BUFF,
        "stacking_type": StackingType.ADDITIVE,
        "description": "When hitting with a melee weapon, heal for an amount equal to min(DamageDealt, Vampirism_Stacks)"
    },
    "Invulnerability": {
        "type": EffectType.BUFF,
        "stacking_type": StackingType.DURATION,
        "description": "Prevents all incoming damage for the duration"
    },
    
    # Debuffs
    "Blind": {
        "type": EffectType.DEBUFF,
        "stacking_type": StackingType.ADDITIVE,
        "description": "Decreases accuracy by 5% per stack"
    },
    "Cold": {
        "type": EffectType.DEBUFF,
        "stacking_type": StackingType.ADDITIVE,
        "description": "All item cooldowns trigger slower. EffectiveCooldown = BaseCooldown * (1 + 0.02 * Cold_Stacks)"
    },
    "Poison": {
        "type": EffectType.DEBUFF,
        "stacking_type": StackingType.ADDITIVE,
        "description": "Deals 1 damage per stack every 2 seconds. This damage bypasses Block"
    },
    "Stun": {
        "type": EffectType.DEBUFF,
        "stacking_type": StackingType.DURATION,
        "description": "Pauses all item cooldowns for the duration of the stun"
    }
}


def create_status_effect(name: str, stacks: int = 1, duration: float = 0.0) -> Optional[StatusEffect]:
    """Factory function to create a status effect"""
    config = STATUS_EFFECT_CONFIGS.get(name)
    if not config:
        return None
        
    return StatusEffect(
        name=name,
        effect_type=config["type"],
        stacking_type=config["stacking_type"],
        stacks=stacks,
        duration=duration,
        applied_time=time.time()
    )
