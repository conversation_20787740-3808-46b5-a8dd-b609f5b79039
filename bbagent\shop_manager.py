import random
from .item import Item

class ShopManager:
    """
    Manages the shop, including item generation, rerolling, and sales.
    """
    def __init__(self, item_data, player_class):
        self.item_data = item_data
        self.player_class = player_class
        self.shop_items = []
        self.reroll_cost = 1
        self.items_seen_since_last_sale = 0
        self.items_seen_since_last_bag = 0
        self.use_shop_blacklist = False # As per DeepResearch.txt

        # Rarity probabilities by round (from DeepResearch.txt)
        self.rarity_probabilities = {
            1: {'Common': 90, 'Rare': 10, 'Epic': 0, 'Legendary': 0, 'Godly': 0},
            2: {'Common': 84, 'Rare': 15, 'Epic': 1, 'Legendary': 0, 'Godly': 0},
            3: {'Common': 75, 'Rare': 20, 'Epic': 5, 'Legendary': 0, 'Godly': 0},
            4: {'Common': 64, 'Rare': 25, 'Epic': 10, 'Legendary': 1, 'Godly': 0},
            5: {'Common': 45, 'Rare': 35, 'Epic': 15, 'Legendary': 5, 'Godly': 0},
            6: {'Common': 29, 'Rare': 40, 'Epic': 20, 'Legendary': 10, 'Godly': 1},
            7: {'Common': 20, 'Rare': 35, 'Epic': 25, 'Legendary': 15, 'Godly': 5},
            8: {'Common': 20, 'Rare': 30, 'Epic': 25, 'Legendary': 15, 'Godly': 10},
            9: {'Common': 20, 'Rare': 28, 'Epic': 25, 'Legendary': 15, 'Godly': 12},
            10: {'Common': 20, 'Rare': 25, 'Epic': 25, 'Legendary': 15, 'Godly': 15},
            11: {'Common': 20, 'Rare': 23, 'Epic': 23, 'Legendary': 17, 'Godly': 17},
            12: {'Common': 20, 'Rare': 20, 'Epic': 20, 'Legendary': 20, 'Godly': 20},
            13: {'Common': 20, 'Rare': 20, 'Epic': 20, 'Legendary': 20, 'Godly': 20},
            14: {'Common': 20, 'Rare': 20, 'Epic': 20, 'Legendary': 20, 'Godly': 20},
            15: {'Common': 20, 'Rare': 20, 'Epic': 20, 'Legendary': 20, 'Godly': 20},
            16: {'Common': 20, 'Rare': 20, 'Epic': 20, 'Legendary': 20, 'Godly': 20},
            17: {'Common': 20, 'Rare': 20, 'Epic': 20, 'Legendary': 20, 'Godly': 20},
            18: {'Common': 20, 'Rare': 20, 'Epic': 20, 'Legendary': 20, 'Godly': 20},
        }

    def generate_shop_items(self, current_round, subclass=None):
        """
        Generates a new set of items for the shop based on the current round and subclass.
        """
        self.shop_items = []
        rarity_weights = self.rarity_probabilities.get(current_round, self.rarity_probabilities[18])

        for _ in range(5): # 5 items in the shop
            # Pity timers
            if self.items_seen_since_last_bag >= 10:
                # Force a bag to appear
                item = self.get_random_item_by_type("Bag", subclass)
            elif self.items_seen_since_last_sale >= 16:
                 # Force a sale to appear
                item = self.get_random_item(rarity_weights, subclass)
                item.on_sale = True
            else:
                item = self.get_random_item(rarity_weights, subclass)

            if item:
                self.shop_items.append(item)
                self.items_seen_since_last_bag += 1
                self.items_seen_since_last_sale += 1
                if item.item_type == "Bag":
                    self.items_seen_since_last_bag = 0
                if hasattr(item, 'on_sale') and item.on_sale:
                    self.items_seen_since_last_sale = 0
        
        return self.shop_items

    def get_random_item(self, rarity_weights, subclass):
        # Select rarity
        rarity = random.choices(list(rarity_weights.keys()), weights=list(rarity_weights.values()), k=1)[0]
        return self.get_random_item_by_rarity(rarity, subclass)

    def get_random_item_by_rarity(self, rarity, subclass):
        # Filter items by rarity, class, and subclass
        available_items = [
            item for item in self.item_data['items'].values() 
            if item.get('rarity') == rarity and \
               (item.get('class') == self.player_class or item.get('class') == 'Neutral')
        ]

        # Add subclass items to the pool
        if subclass and subclass in self.subclass_item_pools:
            subclass_items = [
                item for item in self.item_data['items'].values()
                if item['name'] in self.subclass_item_pools[subclass]
            ]
            available_items.extend(subclass_items)
        
        if not available_items:
            return None

        # Select item
        item_info = random.choice(available_items)
        item = Item(
            name=item_info['name'],
            grid_layout=item_info.get('grid_layout', []),
            grid_size=item_info.get('grid_size', (0, 0)),
            item_type=item_info.get('type', 'other')
        )
        
        # Handle sales
        if random.random() < 0.10: # 10% chance of being on sale
            item.on_sale = True
            self.items_seen_since_last_sale = 0

        return item

    def get_random_item_by_type(self, item_type, subclass):
        # Filter items by type, class, and subclass
        available_items = [
            item for item in self.item_data['items'].values() 
            if item.get('type') == item_type and \
               (item.get('class') == self.player_class or item.get('class') == 'Neutral')
        ]
        
        if not available_items:
            return None

        # Select item
        item_info = random.choice(available_items)
        item = Item(
            name=item_info['name'],
            grid_layout=item_info.get('grid_layout', []),
            grid_size=item_info.get('grid_size', (0, 0)),
            item_type=item_info.get('type', 'other')
        )
        return item

    def reroll(self, current_round, subclass=None):
        """
        Rerolls the shop items and updates the reroll cost.
        """
        self.generate_shop_items(current_round, subclass)
        if self.reroll_cost == 1:
            # First four rerolls cost 1 gold
            # This is a simplified logic, a more accurate implementation would track the number of rerolls
            pass
        else:
            self.reroll_cost = 2
        return self.shop_items
