from typing import Optional, List, Tuple

class Item:
    """
    Represents a single item in the game, holding all its static and dynamic data.
    """
    def __init__(self, name, grid_layout, grid_size, item_type, rarity, item_class, subtype, cost, sockets, effects):
        self.name = name
        self.grid_layout = grid_layout
        self.grid_size = grid_size
        self.item_type = item_type
        self.rarity = rarity
        self.item_class = item_class
        self.subtype = subtype
        self.cost = cost
        self.sockets = sockets
        self.effects = effects

        # Dynamic properties
        self.current_rotation = 0
        self.position = (0, 0) # Position on the grid
        self.on_sale = False
        self.placement_order = 0
    
    def __repr__(self):
        return f"Item(name='{self.name}', position={self.position})"