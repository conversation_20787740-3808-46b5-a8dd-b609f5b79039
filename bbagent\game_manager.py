from time import sleep
from .inventory_manager import InventoryManager
from .recipe_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .utils import load_item_data, load_recipe_data
from .item import Item

class GameManager:
    """
    The central orchestrator of the agent. It manages the game state,
    coordinates the different managers, and executes the main game loop.
    """
    def __init__(self, player_class="Ranger"):
        """Initializes the GameManager, loading all necessary resources and setting up the initial game state."""
        self.item_data = load_item_data()
        recipes_data = load_recipe_data().get('recipes', [])
        if not recipes_data:
            raise ValueError("No recipes found in data/recipes.json or file is empty.")
        
        self.inventory_manager = InventoryManager()
        self.recipe_manager = RecipeManager(recipes_data)
        self.current_inventory_items = []

        # Section 1.4: Game Progression and Resource Allocation
        self.resource_schedule = {
            1: {'gold': 12, 'max_health': 25}, 2: {'gold': 9, 'max_health': 35},
            3: {'gold': 9, 'max_health': 45}, 4: {'gold': 9, 'max_health': 55},
            5: {'gold': 10, 'max_health': 70}, 6: {'gold': 10, 'max_health': 85},
            7: {'gold': 11, 'max_health': 100}, 8: {'gold': 21, 'max_health': 115},
            9: {'gold': 12, 'max_health': 130}, 10: {'gold': 12, 'max_health': 150},
            11: {'gold': 13, 'max_health': 170}, 12: {'gold': 13, 'max_health': 190},
            13: {'gold': 14, 'max_health': 210}, 14: {'gold': 14, 'max_health': 230},
            15: {'gold': 15, 'max_health': 260}, 16: {'gold': 15, 'max_health': 290},
            17: {'gold': 15, 'max_health': 320}, 18: {'gold': 15, 'max_health': 350}
        }

        # Section 1.2: Player State Vector
        self.current_round = 1
        self.health = self.resource_schedule[self.current_round]['max_health']
        self.gold = self.resource_schedule[self.current_round]['gold']
        self.lives = 5
        self.trophies = 0
        self.stamina = 0.0  # Placeholder, will need a more detailed model
        self.mana = 0       # Placeholder
        
        self.player_class = player_class
        self.subclass = None
        self.rank = "Bronze" # Placeholder

        # Section 1.1: Core Game Loop as a Finite State Machine
        self.game_state = 'shop_phase'  # States: shop_phase, battle_phase, combination_phase

    def update_inventory_state(self, new_items_data: list):
        """
        Updates the inventory with a new set of items, usually from the vision system.
        This clears the old inventory and rebuilds it.
        """
        self.inventory_manager.clear()
        self.current_inventory_items = []
        for item_info in new_items_data:
            item_name = item_info.get("name")
            item_details = self.item_data.get("items", {}).get(item_name)
            if item_details:
                item = Item(
                    name=item_name,
                    grid_layout=item_details.get("grid_layout", []),
                    grid_size=item_details.get("grid_size", (0, 0)),
                    item_type=item_details.get("type", "other"),
                    rarity=item_details.get("rarity", "Common"),
                    item_class=item_details.get("class", "Neutral"),
                    subtype=item_details.get("subtype", []),
                    cost=item_details.get("cost", 0),
                    sockets=item_details.get("sockets", 0),
                    effects=item_details.get("effects", [])
                )
                placement = self.inventory_manager.find_placement_with_backtracking(item)
                if placement:
                    pos, angle = placement
                    item.current_rotation = angle
                    self.inventory_manager.add_item_to_grid(item, pos)
                    self.current_inventory_items.append(item)
                else:
                    print(f"Could not find placement for {item.name}")

    def run(self):
        """The main game loop that represents one cycle of the agent's logic."""
        print("Game Manager is running...")
        while self.lives > 0 and self.current_round <= 18:
            if self.game_state == 'shop_phase':
                self.handle_shop_phase()
            elif self.game_state == 'battle_phase':
                self.handle_battle_phase()
            elif self.game_state == 'combination_phase':
                self.handle_combination_phase()
            
            # In a real scenario, this would be event-driven.
            sleep(1) # Placeholder delay

    def start_battle(self):
        """Transitions the game state from the shop to the battle phase."""
        print("Starting battle...")
        self.game_state = 'battle_phase'

    def handle_shop_phase(self):
        """
        Manages the agent's actions during the shop phase.
        This is where the RL agent will make decisions.
        """
        print(f"--- Round {self.current_round} - Shop Phase ---")
        print(f"Gold: {self.gold}, Health: {self.health}, Lives: {self.lives}, Trophies: {self.trophies}")
        
        # Generate shop items
        shop_items = self.shop_manager.generate_shop_items(self.current_round, self.subclass)
        print("Shop items:", [item.name for item in shop_items])

        # Placeholder for agent's actions (buy, sell, reroll, etc.)
        # For now, we will just assume the agent is done and start the battle.
        self.start_battle()

    def handle_combination_phase(self):
        """Handles the automatic combination of items after a battle."""
        print("--- Combination Phase ---")
        recipe_to_craft = self.recipe_manager.check_for_combinations(self.inventory_manager)
        if recipe_to_craft:
            print(f"Found a valid recipe: {recipe_to_craft.result}")
            self.execute_combination(recipe_to_craft)
        
        # Move to the next round
        self.current_round += 1
        if self.current_round <= 18:
            self.gold = self.resource_schedule[self.current_round]['gold']
            self.health = self.resource_schedule[self.current_round]['max_health']
            self.game_state = 'shop_phase'

    def execute_combination(self, recipe: 'Recipe'):
        """
        Removes ingredient items and adds the resulting item to the inventory.
        """
        print(f"Executing combination for: {recipe.result}")
        ingredients_to_remove = []
        
        # Find the item instances corresponding to the recipe ingredients
        for needed_ingredient in recipe.ingredients:
            for item in self.current_inventory_items:
                if item.name == needed_ingredient:
                    ingredients_to_remove.append(item)
                    self.current_inventory_items.remove(item)
                    break
        
        # Remove the ingredients from the inventory grid
        for item in ingredients_to_remove:
            self.inventory_manager.remove_item_from_grid(item)
            
        print(f"Removed: {[item.name for item in ingredients_to_remove]}")

        # Create and add the new item
        new_item_details = self.item_data.get("items", {}).get(recipe.result)
        if new_item_details:
            new_item = Item(
                name=recipe.result,
                grid_layout=new_item_details.get("grid_layout", []),
                grid_size=new_item_details.get("grid_size", (0, 0)),
                item_type=new_item_details.get("type", "other")
            )
            
            placement = self.inventory_manager.find_placement_with_backtracking(new_item)
            if placement:
                pos, angle = placement
                new_item.current_rotation = angle
                self.inventory_manager.add_item_to_grid(new_item, pos)
                self.current_inventory_items.append(new_item)
                print(f"Added: {new_item.name} to inventory.")
            else:
                print(f"Failed to place {new_item.name} in inventory. Inventory might be full.")
        else:
            print(f"Could not find item details for {recipe.result}")

    def handle_battle_phase(self):
        """Monitors the battle and prepares for the next phase."""
        print("--- Battle Phase ---")
        
        # Placeholder for opponent generation
        opponent_inventory = InventoryManager() 

        # Create and run the combat simulation
        combat = CombatManager(self.inventory_manager, opponent_inventory)
        result = combat.simulate_battle()

        if result == "win":
            print("Battle won!")
            self.trophies += 1
        else:
            print("Battle lost!")
            self.lives -= 1
        
        self.game_state = 'combination_phase'