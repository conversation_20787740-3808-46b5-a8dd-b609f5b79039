"""
Resource Management System for Backpack Battles Simulation

Implements stamina and mana systems as defined in DeepResearch.txt Section 3.3.
Handles resource generation, consumption, and the critical "Out of Stamina" state.
"""

from typing import Dict, List, Optional
from dataclasses import dataclass
import time


@dataclass
class ResourceState:
    """Represents the current state of a resource"""
    current: float
    maximum: float
    regen_rate: float  # Per second
    last_update: float = 0.0
    
    def update(self, current_time: float) -> None:
        """Update resource based on time passed and regeneration"""
        if self.last_update == 0.0:
            self.last_update = current_time
            return
            
        delta_time = current_time - self.last_update
        self.last_update = current_time
        
        # Apply regeneration
        self.current = min(self.maximum, self.current + (self.regen_rate * delta_time))
    
    def consume(self, amount: float) -> bool:
        """Attempt to consume resource. Returns True if successful."""
        if self.current >= amount:
            self.current -= amount
            return True
        return False
    
    def add(self, amount: float) -> None:
        """Add resource (from items, etc.)"""
        self.current = min(self.maximum, self.current + amount)
    
    def set_maximum(self, new_max: float) -> None:
        """Set new maximum, adjusting current if needed"""
        self.maximum = new_max
        self.current = min(self.current, self.maximum)


class ResourceManager:
    """Manages all resources for a player"""
    
    def __init__(self, max_stamina: float = 10.0, max_mana: int = 0):
        # Stamina system - continuous resource
        self.stamina = ResourceState(
            current=max_stamina,
            maximum=max_stamina,
            regen_rate=1.0  # 1.0 stamina per second base rate
        )
        
        # Mana system - discrete resource
        self.mana = ResourceState(
            current=float(max_mana),
            maximum=float(max_mana),
            regen_rate=0.0  # Mana doesn't regenerate passively
        )
        
        # Track items waiting for stamina
        self.stamina_waiting_items: List[str] = []
        
    def update(self, current_time: float, status_effects=None) -> List[str]:
        """Update all resources and return list of items that can now activate"""
        # If current_time is a delta time (common case), treat it as such
        if current_time < 100:  # Assume values < 100 are delta times
            if self.stamina.last_update == 0.0:
                self.stamina.last_update = 0.0
            delta_time = current_time
            self.stamina.current = min(self.stamina.maximum,
                                     self.stamina.current + (self.stamina.regen_rate * delta_time))
        else:
            # Apply status effect modifiers to regen rates
            stamina_regen_modifier = 1.0
            if status_effects:
                # Example: Topaz gems might increase stamina regen
                # This would be implemented based on specific item effects
                pass

            original_regen = self.stamina.regen_rate
            self.stamina.regen_rate = 1.0 * stamina_regen_modifier

            # Update resources
            self.stamina.update(current_time)
            self.mana.update(current_time)

            # Restore original regen rate
            self.stamina.regen_rate = original_regen
        
        # Check if any waiting items can now activate
        ready_items = []
        for item_id in self.stamina_waiting_items[:]:  # Copy list to avoid modification during iteration
            # This would need to check the actual stamina cost of the item
            # For now, assume we can check this externally
            ready_items.append(item_id)
            self.stamina_waiting_items.remove(item_id)
        
        return ready_items
    
    def can_use_stamina(self, cost: float) -> bool:
        """Check if enough stamina is available"""
        return self.stamina.current >= cost
    
    def use_stamina(self, cost: float, item_id: str = None) -> bool:
        """Attempt to use stamina. If insufficient, add item to waiting list."""
        if self.stamina.consume(cost):
            return True
        else:
            # Out of stamina - add to waiting list
            if item_id and item_id not in self.stamina_waiting_items:
                self.stamina_waiting_items.append(item_id)
            return False
    
    def can_use_mana(self, cost: int) -> bool:
        """Check if enough mana is available"""
        return int(self.mana.current) >= cost
    
    def use_mana(self, cost: int) -> bool:
        """Attempt to use mana"""
        return self.mana.consume(float(cost))
    
    def add_mana(self, amount: int) -> None:
        """Add mana (from items like Mana Orb, Blueberries)"""
        self.mana.add(float(amount))
    
    def add_stamina(self, amount: float) -> None:
        """Add stamina (from items)"""
        self.stamina.add(amount)
    
    def modify_max_stamina(self, amount: float) -> None:
        """Modify maximum stamina (from items like Stamina Sack)"""
        new_max = max(1.0, self.stamina.maximum + amount)
        self.stamina.set_maximum(new_max)
    
    def modify_max_mana(self, amount: int) -> None:
        """Modify maximum mana"""
        new_max = max(0, int(self.mana.maximum) + amount)
        self.mana.set_maximum(float(new_max))
    
    def modify_stamina_regen(self, multiplier: float) -> None:
        """Modify stamina regeneration rate (from percentage-based effects)"""
        self.stamina.regen_rate = 1.0 * multiplier
    
    def get_stamina_info(self) -> Dict[str, float]:
        """Get current stamina information"""
        return {
            "current": self.stamina.current,
            "maximum": self.stamina.maximum,
            "regen_rate": self.stamina.regen_rate,
            "percentage": (self.stamina.current / self.stamina.maximum) * 100
        }
    
    def get_mana_info(self) -> Dict[str, int]:
        """Get current mana information"""
        return {
            "current": int(self.mana.current),
            "maximum": int(self.mana.maximum)
        }
    
    def is_item_waiting_for_stamina(self, item_id: str) -> bool:
        """Check if an item is waiting for stamina"""
        return item_id in self.stamina_waiting_items
    
    def remove_from_stamina_wait(self, item_id: str) -> None:
        """Remove an item from the stamina waiting list"""
        if item_id in self.stamina_waiting_items:
            self.stamina_waiting_items.remove(item_id)
    
    def get_waiting_items(self) -> List[str]:
        """Get list of items waiting for stamina"""
        return self.stamina_waiting_items.copy()
    
    def reset(self) -> None:
        """Reset all resources to maximum (for new battle)"""
        self.stamina.current = self.stamina.maximum
        self.mana.current = self.mana.maximum
        self.stamina_waiting_items.clear()
    
    def to_dict(self) -> Dict:
        """Export resource state for serialization"""
        return {
            "stamina": {
                "current": self.stamina.current,
                "maximum": self.stamina.maximum,
                "regen_rate": self.stamina.regen_rate
            },
            "mana": {
                "current": int(self.mana.current),
                "maximum": int(self.mana.maximum)
            },
            "waiting_items": self.stamina_waiting_items.copy()
        }
    
    def from_dict(self, data: Dict) -> None:
        """Import resource state from serialization"""
        if "stamina" in data:
            s = data["stamina"]
            self.stamina.current = s.get("current", 10.0)
            self.stamina.maximum = s.get("maximum", 10.0)
            self.stamina.regen_rate = s.get("regen_rate", 1.0)
        
        if "mana" in data:
            m = data["mana"]
            self.mana.current = float(m.get("current", 0))
            self.mana.maximum = float(m.get("maximum", 0))
        
        if "waiting_items" in data:
            self.stamina_waiting_items = data["waiting_items"].copy()


class WeaponStaminaCalculator:
    """Utility class for calculating weapon stamina costs and DPS"""
    
    @staticmethod
    def calculate_stamina_cost_per_second(stamina_cost: float, cooldown: float) -> float:
        """Calculate stamina cost per second for a weapon"""
        if cooldown <= 0:
            return float('inf')
        return stamina_cost / cooldown
    
    @staticmethod
    def calculate_effective_dps(base_dps: float, stamina_cost: float, cooldown: float, 
                              current_stamina_regen: float) -> float:
        """Calculate effective DPS accounting for stamina limitations"""
        stamina_per_second = WeaponStaminaCalculator.calculate_stamina_cost_per_second(
            stamina_cost, cooldown
        )
        
        if stamina_per_second <= current_stamina_regen:
            # Stamina is not limiting factor
            return base_dps
        else:
            # Stamina limits the effective attack rate
            effective_attack_rate = current_stamina_regen / stamina_cost
            return base_dps * (effective_attack_rate * cooldown)
    
    @staticmethod
    def time_until_next_attack(stamina_cost: float, current_stamina: float, 
                             stamina_regen: float) -> float:
        """Calculate time until weapon can attack again"""
        if current_stamina >= stamina_cost:
            return 0.0
        
        stamina_needed = stamina_cost - current_stamina
        return stamina_needed / stamina_regen if stamina_regen > 0 else float('inf')
