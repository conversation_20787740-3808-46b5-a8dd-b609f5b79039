import sqlite3
import json
from typing import List, Dict, Any, Optional, Union

class Item:
    """Represents a static item definition loaded from the database."""
    def __init__(self, data: Union[sqlite3.Row, Dict[str, Any]]):
        if isinstance(data, dict):
            self.id: int = data['id']
            self.name: str = data['name']
            self.rarity: str = data['rarity']
            self.cost: int = data['cost']
            self.item_class: str = data['item_class']
            self.item_type: str = data['item_type']
            self.shape: List[List[int]] = data['shape'] if isinstance(data['shape'], list) else json.loads(data['shape'])
            self.description: str = data['description']
            self.raw_stats: Dict[str, Any] = data['raw_stats'] if isinstance(data['raw_stats'], dict) else (json.loads(data['raw_stats']) if isinstance(data['raw_stats'], str) else data['raw_stats'])
            self.synergy_triggers: List[str] = data['synergy_triggers'] if isinstance(data['synergy_triggers'], list) else (json.loads(data['synergy_triggers']) if isinstance(data['synergy_triggers'], str) else data['synergy_triggers'])
            self.synergy_effects: List[str] = data['synergy_effects'] if isinstance(data['synergy_effects'], list) else (json.loads(data['synergy_effects']) if isinstance(data['synergy_effects'], str) else data['synergy_effects'])
        else:
            self.id: int = data[0]
            self.name: str = data[1]
            self.rarity: str = data[2]
            self.cost: int = data[3]
            self.item_class: str = data[4]
            self.item_type: str = data[5]
            self.shape: List[List[int]] = json.loads(data[6]) if data[6] is not None else []
            self.description: str = data[7]
            self.raw_stats: Dict[str, Any] = json.loads(data[8]) if data[8] is not None else {}
            self.synergy_triggers: Dict[str, Any] = json.loads(data[9]) if data[9] is not None else {}
            self.synergy_effects: Dict[str, Any] = json.loads(data[10]) if data[10] is not None else {}

    def to_dict(self) -> Dict[str, Any]:
        """Returns a dictionary representation of the item."""
        return {
            "id": self.id,
            "name": self.name,
            "rarity": self.rarity,
            "cost": self.cost,
            "item_class": self.item_class,
            "item_type": self.item_type,
            "shape": self.shape,
            "description": self.description,
            "raw_stats": self.raw_stats,
            "synergy_triggers": self.synergy_triggers,
            "synergy_effects": self.synergy_effects,
        }

class ItemInstance:
    """Represents a specific instance of an item, with position and rotation."""
    def __init__(self, item: Item, x: int, y: int, rotation: int = 0):
        self.instance_id: int = id(self)
        self.item: Item = item
        self.x: int = x
        self.y: int = y
        self.rotation: int = rotation  # 0: 0, 1: 90, 2: 180, 3: 270 degrees

    def get_rotated_shape(self) -> List[List[int]]:
        """Returns the item's shape matrix, rotated according to self.rotation."""
        shape = self.item.shape
        rotations = self.rotation % 4
        if rotations == 0:
            return shape
        
        rotated_shape = shape
        for _ in range(rotations):
            transposed = zip(*rotated_shape)
            rotated_shape = [list(row)[::-1] for row in transposed]
            
        return rotated_shape

    def to_dict(self) -> Dict[str, Any]:
        """Returns a dictionary representation of the item instance."""
        return {
            "instance_id": self.instance_id,
            "item": self.item.to_dict(),
            "x": self.x,
            "y": self.y,
            "rotation": self.rotation,
        }

class Backpack:
    """Represents a player's backpack grid and the items within it."""
    def __init__(self, width: int = 9, height: int = 7):
        self.width: int = width
        self.height: int = height
        self.grid: List[List[int]] = [[0] * width for _ in range(height)]
        self.items: Dict[int, ItemInstance] = {}

    def can_place_item(self, item_instance: ItemInstance) -> bool:
        """Checks if an item can be placed at its specified coordinates."""
        rotated_shape = item_instance.get_rotated_shape()

        if not rotated_shape or not rotated_shape[0]:
            return False

        shape_height = len(rotated_shape)
        shape_width = len(rotated_shape[0])

        # Check if the item is within the grid boundaries
        if not (0 <= item_instance.x and item_instance.x + shape_width <= self.width and
                0 <= item_instance.y and item_instance.y + shape_height <= self.height):
            return False

        # Check for collision with other items
        for r_idx, row in enumerate(rotated_shape):
            for c_idx, cell in enumerate(row):
                if cell == 1:  # If this part of the item shape is occupied
                    # Check the corresponding cell in the backpack grid
                    if self.grid[item_instance.y + r_idx][item_instance.x + c_idx] != 0:
                        return False  # Collision detected
        return True  # No collision

    def place_item(self, item_instance: ItemInstance) -> bool:
        """Places an item in the backpack if the location is valid."""
        if not self.can_place_item(item_instance):
            return False

        rotated_shape = item_instance.get_rotated_shape()
        self.items[item_instance.instance_id] = item_instance

        for r_idx, row in enumerate(rotated_shape):
            for c_idx, cell in enumerate(row):
                if cell == 1:
                    self.grid[item_instance.y + r_idx][item_instance.x + c_idx] = item_instance.instance_id
        
        return True

    def remove_item(self, instance_id: int):
        """Removes an item from the backpack grid and item dictionary."""
        if instance_id not in self.items:
            return

        item_instance = self.items.pop(instance_id)
        rotated_shape = item_instance.get_rotated_shape()
        shape_height = len(rotated_shape)
        shape_width = len(rotated_shape[0])

        for r_idx in range(shape_height):
            for c_idx in range(shape_width):
                if rotated_shape[r_idx][c_idx] == 1:
                    grid_y, grid_x = item_instance.y + r_idx, item_instance.x + c_idx
                    if 0 <= grid_y < self.height and 0 <= grid_x < self.width:
                        if self.grid[grid_y][grid_x] == instance_id:
                            self.grid[grid_y][grid_x] = 0

    def to_dict(self) -> Dict[str, Any]:
        """Returns a dictionary representation of the backpack."""
        return {
            "width": self.width,
            "height": self.height,
            "grid": self.grid,
            "items": {str(k): v.to_dict() for k, v in self.items.items()},
        }

class Player:
    """Represents a player in the game."""
    def __init__(self, name: str, player_class: str):
        self.name: str = name
        self.player_class: str = player_class
        self.subclass: Optional[str] = None
        self.health: int = 100
        self.max_health: int = 100
        self.gold: int = 10
        self.lives: int = 5
        self.wins: int = 0
        self.backpack: Backpack = Backpack()
        self.storage: List[ItemInstance] = []

    def to_dict(self) -> Dict[str, Any]:
        """Returns a dictionary representation of the player."""
        return {
            "name": self.name,
            "player_class": self.player_class,
            "subclass": self.subclass,
            "health": self.health,
            "max_health": self.max_health,
            "gold": self.gold,
            "lives": self.lives,
            "wins": self.wins,
            "backpack": self.backpack.to_dict(),
            "storage": [item.to_dict() for item in self.storage],
        }

class GameState:
    """Represents the overall state of the game, including all players."""
    def __init__(self, players: List[Player], current_round: int = 1):
        self.players: List[Player] = players
        self.current_round: int = current_round

    def to_dict(self) -> Dict[str, Any]:
        """Returns a dictionary representation of the game state."""
        return {
            "players": [p.to_dict() for p in self.players],
            "current_round": self.current_round,
        }

def load_all_item_data(db_path: str) -> Dict[str, Item]:
    """
    Connects to the GameData.db, queries the Items table,
    and returns a dictionary of Item objects keyed by item name.
    """
    all_items = {}
    con = None
    try:
        con = sqlite3.connect(db_path)
        con.row_factory = sqlite3.Row
        cur = con.cursor()
        cur.execute("SELECT * FROM Items")
        rows = cur.fetchall()
        for row in rows:
            item = Item(row)
            all_items[item.name] = item
    except sqlite3.Error as e:
        print(f"Database error: {e}")
    finally:
        if con:
            con.close()
    return all_items