"""
Comprehensive Test Suite for Core Simulation Systems

Tests all the newly implemented systems for accuracy and compliance with
the research specifications from DeepResearch.txt.
"""

import unittest
import time
from unittest.mock import Mock, patch

from simulator.status_effects import StatusEffectManager, create_status_effect
from simulator.resources import ResourceManager, WeaponStaminaCalculator
from simulator.combat_events import CombatEventManager, CombatEvent, EventType
from simulator.damage_system import DamageCalculator, AttackInfo, DefenseInfo
from simulator.enhanced_shop import EnhancedShop, PityTimerManager
from simulator.core import Item


class TestStatusEffects(unittest.TestCase):
    """Test the status effects system"""
    
    def setUp(self):
        self.manager = StatusEffectManager()
    
    def test_empower_stacking(self):
        """Test Empower stacks additively"""
        self.manager.add_effect("Empower", stacks=3)
        self.manager.add_effect("Empower", stacks=2)
        
        self.assertEqual(self.manager.get_effect_stacks("Empower"), 5)
        
        damage_bonus = self.manager.get_stat_modifier("damage")
        self.assertEqual(damage_bonus, 5)  # Should add 5 to weapon damage
    
    def test_luck_accuracy_modifier(self):
        """Test Luck affects accuracy correctly"""
        self.manager.add_effect("Luck", stacks=4)
        
        accuracy_mult = self.manager.get_stat_modifier("accuracy_multiplier")
        expected = 1.0 + (0.05 * 4)  # +20% accuracy
        self.assertAlmostEqual(accuracy_mult, expected, places=3)
    
    def test_heat_cooldown_modifier(self):
        """Test Heat affects cooldowns correctly"""
        self.manager.add_effect("Heat", stacks=10)

        cooldown_mult = self.manager.get_stat_modifier("cooldown_multiplier")
        expected = 1.0 / (1.0 + 0.02 * 10)  # Formula: BaseCooldown / (1 + 0.02 * Heat_Stacks)
        self.assertAlmostEqual(cooldown_mult, expected, places=3)
    
    def test_poison_tick_damage(self):
        """Test Poison deals correct damage"""
        self.manager.add_effect("Poison", stacks=3)
        
        # Simulate tick
        triggered = self.manager.update(2.0, 2.0)  # 2 seconds passed
        self.assertIn("Poison", triggered)
    
    def test_status_expiry(self):
        """Test status effects expire correctly"""
        current_time = time.time()
        self.manager.add_effect("Stun", stacks=1, duration=5.0, current_time=current_time)
        
        # Should still be active
        self.assertTrue(self.manager.has_effect("Stun"))
        
        # Should expire after duration
        self.manager.update(current_time + 6.0, 1.0)
        self.assertFalse(self.manager.has_effect("Stun"))


class TestResourceManagement(unittest.TestCase):
    """Test the resource management system"""
    
    def setUp(self):
        self.manager = ResourceManager(max_stamina=10.0, max_mana=5)
    
    def test_stamina_regeneration(self):
        """Test stamina regenerates at 1.0/second"""
        # Use some stamina
        self.manager.use_stamina(5.0)
        self.assertEqual(self.manager.stamina.current, 5.0)
        
        # Simulate 3 seconds
        self.manager.update(3.0)
        self.assertEqual(self.manager.stamina.current, 8.0)
    
    def test_out_of_stamina_state(self):
        """Test weapons wait when out of stamina"""
        # Use all stamina
        self.manager.use_stamina(10.0)
        
        # Try to use more - should fail and add to waiting list
        result = self.manager.use_stamina(2.0, "weapon_1")
        self.assertFalse(result)
        self.assertIn("weapon_1", self.manager.stamina_waiting_items)
    
    def test_mana_discrete_resource(self):
        """Test mana as discrete resource"""
        self.assertEqual(self.manager.mana.current, 5.0)
        
        # Use mana
        result = self.manager.use_mana(3)
        self.assertTrue(result)
        self.assertEqual(int(self.manager.mana.current), 2)
        
        # Try to use more than available
        result = self.manager.use_mana(5)
        self.assertFalse(result)
    
    def test_stamina_cost_calculations(self):
        """Test weapon stamina calculations"""
        # Test stamina cost per second
        cost_per_sec = WeaponStaminaCalculator.calculate_stamina_cost_per_second(2.0, 1.5)
        expected = 2.0 / 1.5
        self.assertAlmostEqual(cost_per_sec, expected, places=3)
        
        # Test time until next attack
        time_until = WeaponStaminaCalculator.time_until_next_attack(3.0, 1.0, 1.0)
        self.assertEqual(time_until, 2.0)  # Need 2 more stamina at 1/sec


class TestCombatEvents(unittest.TestCase):
    """Test the event-driven combat system"""
    
    def setUp(self):
        self.manager = CombatEventManager()
    
    def test_event_ordering(self):
        """Test events are processed in correct order"""
        # Add events with different times
        self.manager.schedule_item_activation("item1", 0, 2.0, {})
        self.manager.schedule_item_activation("item2", 0, 1.0, {})
        self.manager.schedule_item_activation("item3", 0, 1.5, {})
        
        # Should process in time order
        event1 = self.manager.process_next_event()
        event2 = self.manager.process_next_event()
        event3 = self.manager.process_next_event()
        
        self.assertEqual(event1.source_id, "item2")  # 1.0 time
        self.assertEqual(event2.source_id, "item3")  # 1.5 time
        self.assertEqual(event3.source_id, "item1")  # 2.0 time
    
    def test_start_of_battle_fifo(self):
        """Test start-of-battle effects use FIFO ordering"""
        # Register items in order
        self.manager.register_item_placement("item1")
        self.manager.register_item_placement("item2")
        self.manager.register_item_placement("item3")
        
        # Schedule start-of-battle effects
        items = [
            {"item_id": "item3", "player": 0},
            {"item_id": "item1", "player": 0},
            {"item_id": "item2", "player": 0}
        ]
        self.manager.schedule_start_of_battle_effects(items)
        
        # Should process in placement order (FIFO)
        event1 = self.manager.process_next_event()
        event2 = self.manager.process_next_event()
        event3 = self.manager.process_next_event()
        
        self.assertEqual(event1.source_id, "item1")  # First placed
        self.assertEqual(event2.source_id, "item2")  # Second placed
        self.assertEqual(event3.source_id, "item3")  # Third placed
    
    def test_status_tick_scheduling(self):
        """Test status effects are scheduled correctly"""
        self.manager.schedule_status_tick("Poison", 0, 2.0)
        
        event = self.manager.process_next_event()
        self.assertEqual(event.event_type, EventType.STATUS_TICK)
        self.assertEqual(event.data["effect_name"], "Poison")


class TestDamageSystem(unittest.TestCase):
    """Test the 6-stage damage pipeline"""
    
    def setUp(self):
        self.calculator = DamageCalculator()
    
    def test_base_damage_with_empower(self):
        """Test base damage calculation with Empower"""
        attack = AttackInfo(min_damage=5, max_damage=5, accuracy=1.0)
        attacker_effects = {"Empower": 3}
        defender = DefenseInfo(100, 100, 0, {})
        
        result = self.calculator.calculate_damage(attack, attacker_effects, defender)
        
        self.assertEqual(result.raw_damage, 8)  # 5 + 3 Empower
        self.assertTrue(result.was_hit)
    
    def test_critical_hit_calculation(self):
        """Test critical hit mechanics"""
        attack = AttackInfo(min_damage=10, max_damage=10, accuracy=1.0, crit_chance=1.0)
        attacker_effects = {}
        defender = DefenseInfo(100, 100, 0, {})
        
        result = self.calculator.calculate_damage(attack, attacker_effects, defender)
        
        self.assertTrue(result.was_critical)
        self.assertEqual(result.final_damage, 20)  # 10 * 2.0 crit multiplier
    
    def test_accuracy_with_luck_and_blind(self):
        """Test accuracy calculation with Luck and Blind"""
        attack = AttackInfo(min_damage=5, max_damage=5, accuracy=0.5)
        attacker_effects = {"Luck": 10}  # +50% accuracy
        defender = DefenseInfo(100, 100, 0, {"Blind": 5})  # -25% accuracy
        
        # Mock random to test calculation
        with patch('random.random', return_value=0.6):  # 60% roll
            result = self.calculator.calculate_damage(attack, attacker_effects, defender)
            # Final accuracy: 0.5 * 1.5 * 0.75 = 0.5625 (56.25%)
            # 60% roll should miss
            self.assertFalse(result.was_hit)
    
    def test_block_application(self):
        """Test block damage reduction"""
        attack = AttackInfo(min_damage=15, max_damage=15, accuracy=1.0)
        attacker_effects = {}
        defender = DefenseInfo(100, 100, 10, {})  # 10 block
        
        result = self.calculator.calculate_damage(attack, attacker_effects, defender)
        
        self.assertEqual(result.final_damage, 5)  # 15 - 10 block
        self.assertEqual(result.block_consumed, 10)
        self.assertTrue(result.was_blocked)
    
    def test_vampirism_on_hit(self):
        """Test Vampirism on-hit effect"""
        attack = AttackInfo(min_damage=8, max_damage=8, accuracy=1.0)
        attacker_effects = {"Vampirism": 5}
        defender = DefenseInfo(100, 100, 0, {})
        
        result = self.calculator.calculate_damage(attack, attacker_effects, defender)
        
        self.assertIn("Vampirism:5", result.effects_triggered)  # Should heal 5 (min of damage and stacks)


class TestEnhancedShop(unittest.TestCase):
    """Test the enhanced shop system"""
    
    def setUp(self):
        # Create mock items
        self.mock_items = {
            "Common Item": Mock(spec=Item, name="Common Item", rarity="Common", cost=3, item_class="Neutral"),
            "Rare Item": Mock(spec=Item, name="Rare Item", rarity="Rare", cost=5, item_class="Neutral"),
            "Bag Item": Mock(spec=Item, name="Bag Item", rarity="Common", cost=4, type="Bag", item_class="Neutral"),
            "Unique Item": Mock(spec=Item, name="Unique Item", rarity="Unique", cost=10, item_class="Neutral")
        }
        
        self.shop = EnhancedShop(self.mock_items, use_shop_blacklist=False)
    
    def test_rarity_probabilities(self):
        """Test shop uses correct rarity probabilities"""
        # Round 1 should be 90% Common, 10% Rare
        probabilities = self.shop.RARITY_PROBABILITIES[1]
        self.assertEqual(probabilities["Common"], 0.90)
        self.assertEqual(probabilities["Rare"], 0.10)
        self.assertEqual(probabilities["Epic"], 0.00)
    
    def test_pity_timer_sale(self):
        """Test sale pity timer forces sale"""
        pity = PityTimerManager()
        pity.items_since_last_sale = 16  # At threshold
        
        self.assertTrue(pity.should_force_sale())
        
        pity.reset_sale_counter()
        self.assertEqual(pity.items_since_last_sale, 0)
    
    def test_pity_timer_bag(self):
        """Test bag pity timer forces bag"""
        pity = PityTimerManager()
        pity.items_since_last_bag = 10  # At threshold
        
        self.assertTrue(pity.should_force_bag())
    
    def test_reroll_cost_progression(self):
        """Test reroll cost increases correctly"""
        mock_player = Mock(gold=100)
        
        # First 4 rerolls cost 1 gold
        for i in range(4):
            self.shop.reroll_shop(mock_player, 1, "Neutral")
            self.assertEqual(self.shop.state.reroll_cost, 1)
        
        # 5th reroll should cost 2 gold
        self.shop.reroll_shop(mock_player, 1, "Neutral")
        self.assertEqual(self.shop.state.reroll_cost, 2)
    
    def test_unique_item_restriction(self):
        """Test only one unique item can be owned"""
        mock_player = Mock(gold=100)
        
        # Buy first unique item
        self.shop.state.offerings[0] = self.mock_items["Unique Item"]
        self.shop.state.costs[0] = 10
        
        result = self.shop.buy_item(mock_player, 0)
        self.assertTrue(result)
        self.assertTrue(self.shop.unique_item_owned)
        
        # Try to buy another unique item
        self.shop.state.offerings[1] = self.mock_items["Unique Item"]
        self.shop.state.costs[1] = 10
        
        result = self.shop.buy_item(mock_player, 1)
        self.assertFalse(result)  # Should fail


if __name__ == '__main__':
    # Run all tests
    unittest.main(verbosity=2)
